pipeline {
    agent any
    
    parameters {
        string(name: 'DEPLOYMENT_NAME', description: 'Deployment Name', defaultValue: 'takafuldev-wf')
        choice(name: 'NAMESPACE', choices: ['takaful-dev'], description: 'Kubernates Namespace')
        choice(name: 'DOCKER_REGISTRY', choices: ['localhost:5000'], description: 'Docker registry')
        choice(name: 'HOST', choices: ['takaful-dev-webform.srv.aeliasoft.com'], description: 'Host Name')
        choice(name: 'ENV', choices: ['dev','stg','prod'], description: 'Environment')
    }

    stages {
        
        stage('docker-build') {
            steps {
                sh 'docker build . -t ${DOCKER_REGISTRY}/${DEPLOYMENT_NAME}:${BUILD_NUMBER}'
                sh 'docker push ${DOCKER_REGISTRY}/${DEPLOYMENT_NAME}:${BUILD_NUMBER}'
            }
        }

        stage('k8s-deploy') {
            steps {
                dir("deployments/${ENV}") {
                    sh 'sed -i "s|@image-name@|${DOCKER_REGISTRY}/${DEPLOYMENT_NAME}:${BUILD_NUMBER}|g" deployment.yaml'
                    sh 'sed -i "s|@HOST@|${HOST}|g" ingress.yaml'
                    sh 'microk8s kubectl apply -f . -n ${NAMESPACE}'
                }
            }
        }
    }
}
