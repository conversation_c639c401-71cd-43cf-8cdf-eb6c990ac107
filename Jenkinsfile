// Simplified Jenkins<PERSON><PERSON> for Takaful Webform Building using Kaniko with ACR
// This version uses Azure Container Registry with minimal authentication complexity

pipeline {
    agent {
        kubernetes {
            yaml """
apiVersion: v1
kind: Pod
spec:
  serviceAccountName: kaniko
  containers:
  - name: kaniko
    image: gcr.io/kaniko-project/executor:debug
    command:
    - /busybox/sh
    tty: true
    volumeMounts:
    - name: docker-config
      mountPath: /kaniko/.docker/
  - name: kubectl
    image: alpine/k8s:1.28.2
    command:
    - /bin/sh
    tty: true
  volumes:
  - name: docker-config
    secret:
      secretName: acr-docker-config
      items:
      - key: .dockerconfigjson
        path: config.json
"""
        }
    }

    environment {
        ACR_REGISTRY = 'takafulregistry.azurecr.io'
        ACR_NAME = 'takafulregistry'
    }

    parameters {
        string(name: 'DEPLOYMENT_NAME', description: 'Deployment Name', defaultValue: 'takaful-webflow')
        choice(name: 'NAMESPACE', choices: ['takaful-dev', 'takaful-prod'], description: 'Kubernetes Namespace')
        choice(name: 'HOST', choices: ['takaful-dev-webform.srv.aeliasoft.com'], description: 'Host Name')
        choice(name: 'ENV', choices: ['dev','stg','prod'], description: 'Environment')
    }

    stages {
        stage('Docker Build & Push to ACR') {
            steps {
                container('kaniko') {
                    script {
                        echo "🔐 ACR Build Information:"
                        echo "   - ACR Registry: ${ACR_REGISTRY}"
                        echo "   - Deployment: ${DEPLOYMENT_NAME}"
                        echo "   - Environment: ${ENV}"
                        echo "   - Build Number: ${BUILD_NUMBER}"

                        echo "🏗️ Building Docker image with Kaniko for ACR..."

                        // Define image name with environment prefix
                        def imageTag = "${ENV}-${BUILD_NUMBER}"
                        def fullImageName = "${ACR_REGISTRY}/${DEPLOYMENT_NAME}:${imageTag}"
                        def latestImageName = "${ACR_REGISTRY}/${DEPLOYMENT_NAME}:${ENV}-latest"

                        echo "📋 Image Details:"
                        echo "   - Image Tag: ${imageTag}"
                        echo "   - Full Image: ${fullImageName}"
                        echo "   - Latest Tag: ${latestImageName}"

                        // List files to verify checkout
                        sh 'ls -la'

                        // Debug: Check Kaniko Docker config
                        sh '''
                            echo "🔍 Checking Kaniko Docker config..."
                            ls -la /kaniko/.docker/
                            if [ -f "/kaniko/.docker/config.json" ]; then
                                echo "✅ config.json found"
                                cat /kaniko/.docker/config.json
                            else
                                echo "❌ config.json not found"
                            fi
                        '''

                        // Check if Dockerfile exists
                        sh '''
                            if [ -f "Dockerfile" ]; then
                                echo "✅ Dockerfile found"
                                cat Dockerfile
                            else
                                echo "❌ Dockerfile not found in repository root"
                                exit 1
                            fi
                        '''

                        // Build and push with Kaniko to ACR
                        sh """
                            echo "🚀 Starting Kaniko build for ACR..."
                            /kaniko/executor \\
                                --dockerfile=Dockerfile \\
                                --context=. \\
                                --destination=${fullImageName} \\
                                --destination=${latestImageName} \\
                                --cache=true \\
                                --verbosity=info
                        """

                        echo "✅ Docker image built and pushed to ACR successfully!"
                        echo "✅ Images pushed:"
                        echo "   - ${fullImageName}"
                        echo "   - ${latestImageName}"

                        // Store image name for later stages
                        env.FULL_IMAGE_NAME = fullImageName
                    }
                }
            }
        }

        stage('Deploy to Kubernetes') {
            steps {
                container('kubectl') {
                    script {
                        echo "🚀 Deploying to Kubernetes..."
                        echo "📋 Deployment Details:"
                        echo "   - Namespace: ${NAMESPACE}"
                        echo "   - Environment: ${ENV}"
                        echo "   - Host: ${HOST}"
                        echo "   - Image: ${env.FULL_IMAGE_NAME}"

                        sh '''
                            echo "🔍 Testing kubectl..."
                            kubectl version --client

                            echo "🔍 Checking directories..."
                            ls -la
                            ls -la deployments/
                        '''

                        dir("deployments/${ENV}") {
                            sh '''
                                echo "📁 In deployment directory"
                                pwd
                                ls -la
                            '''

                            sh "sed -i 's|@image-name@|${env.FULL_IMAGE_NAME}|g' deployment.yaml"
                            sh "sed -i 's|@HOST@|${HOST}|g' ingress.yaml"

                            // Apply ConfigMap first to ensure Oracle configuration is available
                            sh "kubectl apply -f configmap.yaml -n ${NAMESPACE}"
                            sh "kubectl apply -f deployment.yaml -n ${NAMESPACE}"
                            sh "kubectl apply -f service.yaml -n ${NAMESPACE}"
                            sh "kubectl apply -f pvc.yaml -n ${NAMESPACE}"

                            // Apply secrets if they exist
                            sh '''
                                if [ -f "secrets.yaml" ]; then
                                    echo "✅ Applying secrets.yaml"
                                    kubectl apply -f secrets.yaml -n ${NAMESPACE}
                                else
                                    echo "⚠️ secrets.yaml not found, skipping"
                                fi
                            '''

                            sh "kubectl apply -f ingress.yaml -n ${NAMESPACE}"

                            // Wait for deployment to be ready
                            sh "kubectl rollout status deployment/${DEPLOYMENT_NAME} -n ${NAMESPACE} --timeout=300s"
                            sh "kubectl get pods -n ${NAMESPACE}"

                            // Show logs for debugging
                            sh '''
                                echo "🔍 Checking pod logs for Oracle connection..."
                                POD_NAME=$(kubectl get pods -n ${NAMESPACE} -l app=${DEPLOYMENT_NAME} -o jsonpath='{.items[0].metadata.name}')
                                if [ ! -z "$POD_NAME" ]; then
                                    echo "📋 Pod: $POD_NAME"
                                    kubectl logs $POD_NAME -n ${NAMESPACE} --tail=50 || true
                                fi
                            '''
                        }
                    }
                }
            }
        }
    }

    post {
        always {
            echo '📋 Pipeline completed!'
            script {
                if (env.FULL_IMAGE_NAME) {
                    echo "📦 Final Image: ${env.FULL_IMAGE_NAME}"
                }
            }
        }
        success {
            script {
                echo '🎉 Build and deployment successful!'
                echo "✅ Image: ${env.FULL_IMAGE_NAME}"
                echo "✅ Deployed to: ${NAMESPACE}"
                echo "✅ Environment: ${ENV}"
                echo "✅ Host: ${HOST}"
                echo "✅ ACR Repository: ${ACR_REGISTRY}/${DEPLOYMENT_NAME}"
                echo "✅ Oracle Database: Configured via ConfigMap"
            }
        }
        failure {
            script {
                echo '❌ Build or deployment failed!'
                echo "🔍 Check the logs above for details."
                echo "💡 Common issues with ACR:"
                echo "   - Check ACR authentication secret"
                echo "   - Verify Dockerfile exists in repository"
                echo "   - Check network connectivity to ACR"
                echo "   - Verify Kubernetes deployment files exist"
                echo "💡 Common Oracle issues:"
                echo "   - Check Oracle driver installation in Dockerfile"
                echo "   - Verify ConfigMap has correct Oracle settings"
                echo "   - Check Oracle database connectivity"
                echo "   - Verify OCI8 extension is loaded"
            }
        }
    }
}
