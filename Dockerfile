# Use Ubuntu as the base image
FROM ubuntu:latest

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PHP_VERSION=8.3

# Update system and install dependencies
RUN apt-get update && apt-get install -y \
  apt-utils \
  build-essential \
  libzip-dev \
  curl \
  software-properties-common \
  curl \
  unzip \
  wget \
  zip \
  git \
  npm \
  openssl \
  libssl-dev \
  php${PHP_VERSION} \
  php${PHP_VERSION}-cli \
  php${PHP_VERSION}-fpm \
  php${PHP_VERSION}-xml \
  php${PHP_VERSION}-mbstring \
  php${PHP_VERSION}-bcmath \
  php${PHP_VERSION}-curl \
  php${PHP_VERSION}-tokenizer \
  php${PHP_VERSION}-pdo \
  php${PHP_VERSION}-intl \
  php${PHP_VERSION}-zip \
  php${PHP_VERSION}-dev \
  && apt-get clean

RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash -

RUN apt-get install -y nodejs

# Install Oracle Instant Client and OCI8
RUN mkdir -p /opt/oracle
WORKDIR /opt/oracle
RUN  wget -O instantclient-basic-linux.zip "https://download.oracle.com/otn_software/linux/instantclient/2370000/instantclient-basic-linux.x64-*********.01.zip"
RUN  wget -O instantclient-sdk-linux.zip "https://download.oracle.com/otn_software/linux/instantclient/2370000/instantclient-sdk-linux.x64-*********.01.zip"
RUN  wget -O instantclient-tools-linux.zip "https://download.oracle.com/otn_software/linux/instantclient/2370000/instantclient-tools-linux.x64-*********.01.zip"
RUN  unzip instantclient-basic-linux.zip; exit 0
RUN  unzip instantclient-sdk-linux.zip; exit 0
RUN  unzip instantclient-tools-linux.zip; exit 0
RUN  ln -s /opt/oracle/instantclient_23_7 /opt/oracle/instantclient
RUN  echo "/opt/oracle/instantclient" > /etc/ld.so.conf.d/oracle-instantclient.conf
RUN  ldconfig

# Set Oracle environment variables
RUN export LD_LIBRARY_PATH="/opt/oracle/instantclient_23_7:${LD_LIBRARY_PATH}" && \
  export ORACLE_HOME="/opt/oracle/instantclient_23_7" && \
  echo "instantclient,/opt/oracle/instantclient_23_7" | pecl install oci8

RUN  echo "extension=oci8.so" > /etc/php/${PHP_VERSION}/cli/conf.d/20-oci8.ini

RUN curl -O http://launchpadlibrarian.net/646633572/libaio1_0.3.113-4_amd64.deb && \
  dpkg -i libaio1_0.3.113-4_amd64.deb

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Set the working directory
WORKDIR /var/www/html

# Copy application files
COPY . .

# Install PHP dependencies
RUN composer install --no-dev --optimize-autoloader --ignore-platform-req=ext-oci8

# Install Node.js dependencies and build assets
RUN npm install && npm run build

# Set permissions
RUN mkdir -p /var/www/html/storage/framework/cache/data \
    && mkdir -p /var/www/html/storage/framework/cache/sessions \
    && mkdir -p /var/www/html/storage/framework/cache/views \
    && chown -R www-data:www-data /var/www/html \
    && chmod -R 775 /var/www/html/storage \
    && chmod -R 775 /var/www/html/bootstrap/cache 

RUN ln -s /var/www/html/storage/logs /var/www/html/public/logs

# Expose the necessary ports
EXPOSE 80

# Run Laravel migrations on container start
CMD php artisan migrate --force && php -t ./public -S 0.0.0.0:80