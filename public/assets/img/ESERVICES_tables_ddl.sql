-- Start of DDL Script for Table ESERVICES.ICLAIMS_ANNOUNCES
-- Generated 29/01/2025 1:24:50 PM from ESERVICES@NEWAIMSTEST

CREATE TABLE eservices.iclaims_announces
    (
    iclm_chasi VARCHAR2(30)               not null,          
    iclm_plate_no VARCHAR2(30)                    , -- if it is fille by user when made the search  
    ---------     -- given by aims policies except if data does not exists , thin th euser will fill the required data   
    iclm_pol_key_form VARCHAR2(50),  -- given by aims policies 
    iclm_pol_key2_form VARCHAR2(50),
    icl_bill_key_form VARCHAR2(50),
    iclm_branch_no NUMBER(2,0),
    iclm_office_no NUMBER(3,0),
    iclm_maj_ins_type NUMBER(2,0),
    iclm_min_ins_type NUMBER(3,0),
    iclm_doc_no NUMBER(6,0),
    iclm_doc_type NUMBER(2,0),
    iclm_doc_year NUMBER(4,0),
    iclm_ins_st_dt DATE,
    iclm_ins_ed_dt DATE,
    ---- cust info     -- given by aims policies 
    iclm_pol_cust_no NUMBER(12,0),
    iclm_pol_db_acc_no NUMBER(12,0),
    iclm_pol_through NUMBER(5,0),
    iclm_pol_cust_name VARCHAR2(100),
    iclm_pol_cust_idcard VARCHAR2(30),
    iclm_pol_cust_address VARCHAR2(100),
    iclm_pol_cust_mob VARCHAR2(30),
    iclm_pol_cust_job VARCHAR2(100),
    iclm_pol_cust_job_loc VARCHAR2(100),
    -- MOTOR info --  -- given by aims policies 
    iclm_pol_si NUMBER(10,0),
    iclm_pol_comp_excess NUMBER(3,0),
    iclm_pol_tp_excess NUMBER(3,0),
    iclm_pol_drivers VARCHAR2(200),
    
    iclm_pol_make NUMBER(3,0),
    iclm_pol_model VARCHAR2(50),
    iclm_pol_passengers NUMBER(3,0),
    iclm_pol_prod_year NUMBER(4,0),
    iclm_pol_color NUMBER(4,0),
    iclm_pol_use NUMBER(3,0),
    iclm_pol_sp_use NUMBER(3,0),
    iclm_pol_eng_source NUMBER(2,0),
    iclm_pol_cc NUMBER(5,0),
    iclm_pol_load NUMBER(6,3),
-----    SHOULD be Filled
    iclm_mot_lic_stamp_dt DATE,
    iclm_mot_lic_print_dt DATE,
    iclm_mot_lic_end_dt DATE,
    clm_denamo_dt DATE,
--    
    iclm_driver_rel_with_cust NUMBER(2,0) not null ,-- be be select by user , if the rel =1 then customer info have been retieved , rel=2 agent Agent info has been retrieved ,...
    iclm_driver_name VARCHAR2(100) not null,
    iclm_driver_bdate DATE not null,
    iclm_driver_gender NUMBER(1,0) ,
    iclm_driver_address VARCHAR2(100) ,
    iclm_driver_mob VARCHAR2(30) not null,
    iclm_driver_job VARCHAR2(100) ,
    iclm_driver_job_loc VARCHAR2(100) ,
    clm_owner_id_card VARCHAR2(30)    ,
    clm_owner_name VARCHAR2(100)      ,
    clm_owner_tel VARCHAR2(30)        ,
    ---
    iclm_driver_lic_no VARCHAR2(30)   not null,
    iclm_driver_lic_type VARCHAR2(10) not null ,
    iclm_driver_lic_type_desc VARCHAR2(30) not null,
    iclm_driver_lic_match_motor NUMBER(1,0) not null,
    iclm_driver_lic_sdate DATE   not null,
    iclm_driver_lic_edate DATE   not null,
    iclm_driver_lic_1stdate DATE not null ,
    clm_driver_exper_years NUMBER(2,0) not null,
    
    
-----------------------------------------------------------------------    
    iclm_id             INTEGER                       DEFAULT ON NULL eservices.iclm_id20250127.nextval NOT NULL,
    iclm_no             NUMBER(10,0), -- Unique Key
    iclm_acc_year       NUMBER(4,0),  -- Unique Key
    iclm_acc_dt DATE               not null,
    iclm_acc_time NUMBER(5,0)     not null,,
    iclm_reg_dt DATE  not null,
    iclm_reg_time NUMBER(5,0) not null,
    iclm_acc_type NUMBER(3,0)  not null,
    iclm_acc_reason NUMBER(3,0) not null, 
    iclm_acc_place NUMBER(3,0)   not null,
    iclm_acc_place_desc VARCHAR2(4000)  not null,
    iclm_announced_by NUMBER(1,0)  not null, --loockup 
    iclm_is_timely_announced NUMBER(1,0)  not null,
    iclm_does_inves_come_to_loc NUMBER(1,0)  default 1  not null, -- 1/0 yes/No
    iclm_whynot_inves_at_loc VARCHAR2(100)   ,
    iclm_announcer VARCHAR2(100) not null , -- //namne if the same passenger appear same info above, other appear a new data filled by user like agent ....
    iclm_announcer_mob VARCHAR2(100)      ,--same above
    iclm_announcer_idcard VARCHAR2(100)   , --same
    iclm_announcer_address VARCHAR2(100)  ,--same
    iclm_nof_passengers NUMBER(2,0)       , 
    iclm_speed NUMBER(3,0)                ,
    iclm_use_purpose NUMBER(1,0)          ,-- 1 as per policy 2, not match the purpose of policy
    iclm_use_purpose_desc VARCHAR2(100)   , -- if  2, not match the purpose of policy, determine the purpose
    iclm_is_tp NUMBER(1,0) not null, -- yes/No 0 no/1 yes
    iclm_is_comp NUMBER(1,0) not null, -- yes/No 0 no/1 yes
    iclm_is_bi NUMBER(1,0) not null, -- yes/No 0 no/1 yes
    iclm_is_recovery NUMBER(1,0) not null, -- yes/No 0 no/1 yes
    iclm_is_rep_by_police NUMBER(1,0) not null,  -- yes/No 0 no/1 yes Palestin, 2 Israeli
    iclm_why_no_police NUMBER(2,0)  not null,     -- lookup reason of no police  --
    iclm_is_our_responslity NUMBER(1,0) default 0 not null, -- yes/No 0 no/1 yes, 2 both  212��
    iclm_is_damaged NUMBER(1,0)  default 0  not null, -- yes/No 0 no/1 yes
    iclm_resp_by_police NUMBER(1,0) default 0  not null, -- yes/No 0 no/1 yes is responsibility determined by police
    iclm_acc_mot_tp_cnt NUMBER(3,0) default 0  not null, 

    iclm_acc_mot_winch_used NUMBER(1,0)  default 0  not null, -- 0 no inside city area Winch used ,or  no of times used   كم عدد مرات استخدام الونش
    iclm_winch_name VARCHAR2(100),
    iclm_karaj_no NUMBER(10,0) , --lookup
    iclm_karaj_name VARCHAR2(100) ,
    iclm_acc_inj_cnt NUMBER(3,0) default 0  not null, -- عدد المصابين
    iclm_acc_death_cnt NUMBER(2,0) default 0  not null, -- عدد الوفايات
    iclm_ambulance_in_used NUMBER(2,0) default 0  not null, -- in - inside city
    iclm_ambulance_out_used NUMBER(2,0) default 0  not null, -- out - outside city
    iclm_hospital_no NUMBER(3,0),-- should be lookep -- checking no - id
    iclm_hospital_name VARCHAR2(100), 
    iclm_police_report VARCHAR2(100),  -- file name attached 
    iclm_driver_license VARCHAR2(100), -- file name attached
    iclm_motor_license VARCHAR2(100),  -- file name attached
    iclm_policy VARCHAR2(100), -- file name attached
    iclm_tp_docs VARCHAR2(100),  -- file name attached
    iclm_was_photos_at_loc NUMBER(1,0) default 1  not null, -- at location , but may attached through file when the user at backoffice, if the invetigator not at location هل الصور وقت الحادث
    iclm_whynot_photos_at_loc VARCHAR2(100) , --if no appear add reason
    iclm_police_rep_reqsted NUMBER(1,0) not null, -- هل تلمحقق طلب تقرير الشرطة اه، لا
    iclm_does_police_come NUMBER(1,0) not null, --هل جائت الشرطة ؟
    iclm_does_drv_lic_match NUMBER(1,0)not null, -- هل رخصة السائق متوافقة مع التامين  
    iclm_does_mot_lic_match NUMBER(1,0)not null, -- هل رخصة المركبة متوائمة مع وثيقة التامين
    iclm_does_policy_match NUMBER(1,0) not null,  -- هل 
    iclm_loc_photos NUMBER(1,0) not null,
    iclm_mot_front_photo NUMBER(1,0) not null,
    iclm_mot_back_photo NUMBER(1,0 )not null,
    iclm_mot_left_photo NUMBER(1,0) not null,
    iclm_mot_right_photo NUMBER(1,0) not null,
    iclm_mot_chasi_photo NUMBER(1,0) not null,
    iclm_tp_front_photos NUMBER(1,0) not null, --tp طرف ثالث
    iclm_tp_back_photos NUMBER(1,0) not null,
    iclm_tp_left_photos NUMBER(1,0) not null,
    iclm_tp_right_photos NUMBER(1,0) not null,
    iclm_tp_chasi_photos NUMBER(1,0) not null,
    iclm_inves_code VARCHAR2(30) not null,  -- autometically by system invistiger id  رقم الحقق اللي عامل تسجيل دخول
    iclm_inves_name VARCHAR2(100) not null, -- autometically by system
    iclm_acc_desc VARCHAR2(4000) not null,
    iclm_acc_desc2 VARCHAR2(4000),
    iclm_mot_damage_desc VARCHAR2(4000) not null,
    iclm_tp_damage_desc VARCHAR2(4000), --الاضرار الاتجة للطرف الثالث
    iclm_driver_desc VARCHAR2(4000) not null, -- ملاحظات السائق
    iclm_driver_desc_dt DATE default sysdate not null , --autometically by system
    iclm_inves_eval_notes VARCHAR2(4000),-- تو3يات المحقق
    iclm_inves_result NUMBER(2,0) not null , -- id lookup table
    iclm_inves_result_reason VARCHAR2(500) , -- اسباب عدم فتح الحادث
    iclm_inves_rep_dt DATE default sysdate ,
    iclm_inves_mgr_feedback VARCHAR2(4000), -- ملاحاظت المدير
    iclm_inves_mgr_feedback_dt DATE,
    iclm_inves_file_status NUMBER(2,0) default 1 , -- n وضع التحقيق w  -- lookup 
    iclm_follower_as_at VARCHAR2(100) , -- by default the user anem of invetiagtor
    clm_no NUMBER(6,0),
    clm_year NUMBER(4,0),
    iclm_evaluater_code NUMBER(10,0),
    iclm_evaluater_name VARCHAR2(100),
    iclm_evaluater_assigned_dt DATE,
    iclm_user_no NUMBER(3,0)       not null,  --- default user no
    iclm_last_trn_dt DATE   not null,        -- default sysdate date
    iclm_last_trn_time NUMBER(5,0)) not null --- default sysdate time
  NOPARALLEL
  LOGGING
  MONITORING
/


-- Indexes for ESERVICES.ICLAIMS_ANNOUNCES

CREATE INDEX eservices.iclm_ann_pk ON eservices.iclaims_announces
  (
    iclm_id  ASC
  )
NOPARALLEL
LOGGING
/



-- Constraints for ESERVICES.ICLAIMS_ANNOUNCES

ALTER TABLE eservices.iclaims_announces
ADD CONSTRAINT iclm_announces_ukey UNIQUE (iclm_no, iclm_acc_year)
USING INDEX
/


-- End of DDL Script for Table ESERVICES.ICLAIMS_ANNOUNCES

-- Start of DDL Script for Table ESERVICES.ICLM_ACC_INJURED
-- Generated 29/01/2025 1:24:50 PM from ESERVICES@NEWAIMSTEST

CREATE TABLE eservices.iclm_acc_injured
    (iclm_no NUMBER(6,0) ,--- pk   , fk 
    iclm_year NUMBER(4,0) , --- pk ,fk
    iclm_inj_seq NUMBER(3,0) , -- pk
    iclm_inj_aname VARCHAR2(500) not null,
    iclm_inj_result NUMBER(2,0) not null,
    iclm_inj_acc_loc_type NUMBER(2,0) not null,
    iclm_inj_tel VARCHAR2(20)      , 
    iclm_inj_idcard VARCHAR2(15)   , -- not null
    iclm_inj_address VARCHAR2(100) ,
    iclm_inj_dob DATE              , -- not null
    iclm_inj_gender  number        , --not null  ; 1 male,2, femal
    iclm_inj_job VARCHAR2(100)     ,
    iclm_inj_sal VARCHAR2(50)      ,
    iclm_inj_desc VARCHAR2(4000)   , -- 
    iclm_inj_ambul_in_used NUMBER(2,0) default 0 , -- not null 0/1  --- ---- ---- مكرر مع الفوق
    iclm_inj_ambul_out_used NUMBER(2,0) default 0, -- not null 0/1 ---- ---- ---- مكرر مع الفوق 
    iclm_inj_hosp_no NUMBER(3,0)    ,
    iclm_inj_hosp_name VARCHAR2(100),
    iclm_inj_hosp_mail_sent NUMBER(1,0),
    iclm_inj_hosp_mail_sent_dt DATE ,
    iclm_inj_user_no NUMBER(6,0) not null , -- auto user id
    iclm_inj_last_trn_dt DATE    not null         , -- sysdate
    iclm_inj_last_trn_time NUMBER(5,0)  not null  -- sysdate
  ,
  CONSTRAINT ICLM_ACC_INJURED_PK
  PRIMARY KEY (iclm_no, iclm_year, iclm_inj_seq)
  USING INDEX)
  NOPARALLEL
  LOGGING
  MONITORING
/


-- Constraints for ESERVICES.ICLM_ACC_INJURED



-- End of DDL Script for Table ESERVICES.ICLM_ACC_INJURED

-- Start of DDL Script for Table ESERVICES.ICLM_ACC_WITNESS
-- Generated 29/01/2025 1:24:51 PM from ESERVICES@NEWAIMSTEST

CREATE TABLE eservices.iclm_acc_witness
    (
    iclm_no NUMBER(6,0)  ,--- pk   , fk 
    iclm_year NUMBER(4,0) ,--- pk   , fk 
    iclm_witness_seq NUMBER(3,0) ,--- pk   
    iclm_witness_aname VARCHAR2(500) not null,
    iclm_wt_tel VARCHAR2(20) not null,
    iclm_wt_idcard VARCHAR2(15) ,
    iclm_wt_address VARCHAR2(100),
    iclm_wt_user_no NUMBER(6,0)  not null,
    iclm_wt_last_trn_dt DATE default sysdate not null,
    iclm_wt_last_trn_time NUMBER(5,0) default sysdate 
  ,
  CONSTRAINT ICLM_ACC_WITNESS_PK
  PRIMARY KEY (iclm_no, iclm_year, iclm_witness_seq)
  USING INDEX)
  NOPARALLEL
  LOGGING
  MONITORING
/


-- Constraints for ESERVICES.ICLM_ACC_WITNESS



-- End of DDL Script for Table ESERVICES.ICLM_ACC_WITNESS

-- Start of DDL Script for Table ESERVICES.ICLM_TP
-- Generated 29/01/2025 1:24:51 PM from ESERVICES@NEWAIMSTEST

CREATE TABLE eservices.iclm_tp
    (
    iclm_no NUMBER(6,0) ,--- pk   , fk 
    iclm_acc_year NUMBER(4,0) ,--- pk   , fk 
    iclm_tp_serial NUMBER(3,0) ,--- pk   
    iclm_tp_mot_plate_no VARCHAR2(30),
    iclm_tp_mot_chasi_no VARCHAR2(30) not null,  -- 
    iclm_tp_mot_make VARCHAR2(30) not null,
    iclm_tp_mot_model VARCHAR2(30) not null ,
    iclm_tp_mot_eng_source VARCHAR2(30)  not null ,
    iclm_tp_mot_color VARCHAR2(30)  not null ,
    iclm_tp_mot_prod_year VARCHAR2(30)  not null ,
    iclm_tp_mot_use VARCHAR2(30)  not null ,
    iclm_tp_mot_cc NUMBER(4,0)  not null ,
    iclm_tp_mot_load VARCHAR2(30) not null ,-- 
    iclm_tp_is_4tonsplus NUMBER(1,0)  not null , -- 0/1 no/yes-- chechbox
    iclm_tp_mot_edt DATE not null ,
    iclm_tp_mot_denamo_stamp DATE,
    iclm_tp_owner_name VARCHAR2(100)  ,
    iclm_tp_owner_idcard VARCHAR2(30) ,
    iclm_tp_owner_tel VARCHAR2(30)    ,
    iclm_tp_owner_address VARCHAR2(100),
    iclm_tp_driver_name VARCHAR2(100)  not null,--معلومات السائق 
    iclm_tp_driver_idcard VARCHAR2(30)  not null,--معلومات السائق 
    iclm_tp_driver_tel VARCHAR2(30)  not null,--معلومات السائق 
    iclm_tp_driver_address VARCHAR2(100) ,
    iclm_tp_driver_lic_edt DATE  not null,
    iclm_tp_ins_comp VARCHAR2(100) not null,
    iclm_tp_pol_no VARCHAR2(50)  not null,-- رقم البوليصة
    iclm_tp_pol_coverage VARCHAR2(50)  not null, -- التغطية - نوع التامين
    iclm_is_recovery NUMBER(1,0)  default 0 not null,  --0 no recovery 1 , recovery, 2 both
    iclm_acc_mot_winch_used NUMBER(1,0) default 0 not null,  -- 0/1 with winch
    iclm_winch_name VARCHAR2(100),
    iclm_tp_damage_desc             VARCHAR2(4000),
    iclm_tp_garage_no               NUMBER(10,0),
    iclm_tp_garage_name             VARCHAR2(100),
    iclm_tp_garage_dt               DATE,
    iclm_tp_eval_dt                 DATE,
    iclm_tp_eval_location           VARCHAR2(100),
    iclm_tp_photos                  NUMBER(1,0) default 0 not null,-- if photoes taken 1/0 yes/No
    iclm_tp_user_no                 NUMBER(6,0) not null ,
    iclm_tp_last_trn_dt             DATE not null,
    iclm_tp_last_trn_time           NUMBER(5,0) not null
  ,
  CONSTRAINT ICLM_TP_PK
  PRIMARY KEY (iclm_no, iclm_acc_year, iclm_tp_serial)
  USING INDEX)
  NOPARALLEL
  LOGGING
  MONITORING
/


-- Constraints for ESERVICES.ICLM_TP



-- End of DDL Script for Table ESERVICES.ICLM_TP

-- Start of DDL Script for Table ESERVICES.ICLM_TP_PHOTOS
-- Generated 29/01/2025 1:24:51 PM from ESERVICES@NEWAIMSTEST

CREATE TABLE eservices.iclm_tp_photos
    (iclm_no NUMBER(6,0) ,--- pk   , fk 
    iclm_acc_year NUMBER(4,0) ,--- pk   , fk 
    iclm_tp_serial NUMBER(3,0) ,--- pk   , fk 
    iclm_tp_serial_photo_seq NUMBER(3,0 , )--- pk   
    iclm_tp_serial_photo_type NUMBER(3,0) not null,-- front,back,left,right,chasi,driverpolicy,MotorPermit,copyID,....
    iclm_tp_serial_photo_type_desc VARCHAR2(50),-- name to be filled autometaclly 
    iclm_tp_front_photo_file VARCHAR2(200), -- iclmyear||iclmno||_serial_yyyymmdd_seq
    --iclm_tp_front_photos_blob BLOB,
    iclm_user_no NUMBER(6,0) not null,  -- auto
    iclm_last_trn_dt DATE  not null, --AUTO system date 
    iclm_last_trn_time NUMBER(5,0) not null
  ,
  CONSTRAINT ICLM_TP_PH_PK
  PRIMARY KEY (iclm_no, iclm_acc_year, iclm_tp_serial, iclm_tp_serial_photo_seq)
  USING INDEX)
  LOB ("ICLM_TP_FRONT_PHOTOS_BLOB") STORE AS SECUREFILE SYS_LOB0000389716C00008$$
  (
   NOCACHE LOGGING
   CHUNK 8192
  )
  NOPARALLEL
  LOGGING
  MONITORING
/


-- Constraints for ESERVICES.ICLM_TP_PHOTOS



-- End of DDL Script for Table ESERVICES.ICLM_TP_PHOTOS

-- Start of DDL Script for Table ESERVICES.PROD_MOTOR_PHOTOS
-- Generated 29/01/2025 1:24:52 PM from ESERVICES@NEWAIMSTEST

CREATE TABLE eservices.prod_motor_photos
    (
    mph_id   INTEGER                       DEFAULT ON NULL eservices.prod_id20250127.nextval NOT NULL,  --pk
    mph_policy VARCHAR2(50)  ,  -- 
    mph_chasi VARCHAR2(50)  not null ,
    mph_plate_no VARCHAR2(50) ,
    mph_entry_date DATE default sysdate not null, --
    mph_timestamp DATE default sysdate not null, -- not update by user taken once user get the picture
    mph_user VARCHAR2(50)  not null  , -- auto
    mph_user_type NUMBER(2,0) ,  -- by login
    mph_user_type_desc VARCHAR2(50),  -- by login
    mph_user_code NUMBER(15,0),  -- by login
    mph_photo_front VARCHAR2(200) not null,
    mph_photo_back VARCHAR2(200) not null,
    mph_photo_right VARCHAR2(200) not null,
    mph_photo_left VARCHAR2(200) not null,
    mph_photo_chasi VARCHAR2(200) not null,
    mph_photo_6 VARCHAR2(200),
    mph_photo_7 VARCHAR2(200),
    mph_photo_8 VARCHAR2(200)
  ,
  CONSTRAINT PROD_MOTOR_PH_PK
  PRIMARY KEY (mph_id)
  USING INDEX)
  NOPARALLEL
  LOGGING
  MONITORING
/


-- End of DDL Script for Table ESERVICES.PROD_MOTOR_PHOTOS

-- Start of DDL Script for Table ESERVICES.SEC_ROLES
-- Generated 29/01/2025 1:24:52 PM from ESERVICES@NEWAIMSTEST

CREATE TABLE eservices.sec_roles
    (role_id NUMBER(3,0) ,
    role_name VARCHAR2(50)
  ,
  CONSTRAINT SEC_ROLES_PK
  PRIMARY KEY (role_id)
  USING INDEX)
  SEGMENT CREATION IMMEDIATE
  NOPARALLEL
  LOGGING
  MONITORING
/


-- End of DDL Script for Table ESERVICES.SEC_ROLES

-- Start of DDL Script for Table ESERVICES.SEC_USERS
-- Generated 29/01/2025 1:24:53 PM from ESERVICES@NEWAIMSTEST

CREATE TABLE eservices.sec_users
    (user_id NUMBER(10,0) ,
    user_name VARCHAR2(50),
    user_type VARCHAR2(20)
  ,
  CONSTRAINT SEC_USERS_PK
  PRIMARY KEY (user_id)
  USING INDEX)
  SEGMENT CREATION IMMEDIATE
  NOPARALLEL
  LOGGING
  MONITORING
/


-- End of DDL Script for Table ESERVICES.SEC_USERS

-- Start of DDL Script for Table ESERVICES.SEC_USERS_ROLES
-- Generated 29/01/2025 1:24:53 PM from ESERVICES@NEWAIMSTEST

CREATE TABLE eservices.sec_users_roles
    (user_id NUMBER(10,0) ,
    role_id NUMBER(3,0) 
  ,
  CONSTRAINT SEC_USERS_ROLES_PK
  PRIMARY KEY (user_id, role_id)
  USING INDEX)
  SEGMENT CREATION IMMEDIATE
  NOPARALLEL
  LOGGING
  MONITORING
/


-- Constraints for ESERVICES.SEC_USERS_ROLES




-- End of DDL Script for Table ESERVICES.SEC_USERS_ROLES

-- Foreign Key
ALTER TABLE eservices.iclm_acc_injured
ADD CONSTRAINT iclm_acc_injured_fk1 FOREIGN KEY (iclm_no, iclm_year)
REFERENCES ESERVICES.iclaims_announces (iclm_no,iclm_acc_year)
/
-- Foreign Key
ALTER TABLE eservices.iclm_acc_witness
ADD CONSTRAINT iclm_acc_witness_fk1 FOREIGN KEY (iclm_no, iclm_year)
REFERENCES ESERVICES.iclaims_announces (iclm_no,iclm_acc_year)
/
-- Foreign Key
ALTER TABLE eservices.iclm_tp
ADD CONSTRAINT iclm_tp_fk1 FOREIGN KEY (iclm_no, iclm_acc_year)
REFERENCES ESERVICES.iclaims_announces (iclm_no,iclm_acc_year)
/
-- Foreign Key
ALTER TABLE eservices.iclm_tp_photos
ADD CONSTRAINT iclm_tp_ph_fk1 FOREIGN KEY (iclm_no, iclm_acc_year, 
  iclm_tp_serial)
REFERENCES ESERVICES.iclm_tp (iclm_no,iclm_acc_year,iclm_tp_serial)
/
-- Foreign Key
ALTER TABLE eservices.sec_users_roles
ADD CONSTRAINT sec_users_rols_fk1 FOREIGN KEY (user_id)
REFERENCES ESERVICES.sec_users (user_id)
/
ALTER TABLE eservices.sec_users_roles
ADD CONSTRAINT sec_users_rols_fk2 FOREIGN KEY (role_id)
REFERENCES ESERVICES.sec_roles (role_id)
/
-- End of DDL script for Foreign Key(s)
