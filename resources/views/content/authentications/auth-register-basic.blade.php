@extends('layouts/blankLayout')

@section('title', 'Register Basic - Pages')

@section('page-style')
@vite([
  'resources/assets/vendor/scss/pages/page-auth.scss'
])
@endsection


@section('content')
<div class="container-xxl">
  <div class="authentication-wrapper authentication-basic container-p-y">
    <div class="authentication-inner">
      <!-- Register Card -->
      <div class="card px-sm-6 px-0">
        <div class="card-body">
          <!-- Logo -->
          <div class="app-brand justify-content-center mb-6">
            <a href="http://takaful-dev.srv.aeliasoft.com/" class="app-brand-link gap-2">
              <!-- <span class="app-brand-logo demo">@include('_partials.macros',["width"=>25,"withbg"=>'var(--bs-primary)'])</span> -->
              <span class="app-brand-text demo text-heading fw-bold">{{config('variables.templateName')}}</span>
            </a>
          </div>
          <!-- /Logo -->
          <h4 class="mb-1">ابدأ رحلتك معنا  🚀</h4>
          <p class="mb-6">اجعل إدارة خدمات التأمين أسهل وأكثر متعة مع شركة التكافل للتأمين!</p>

          <form id="formAuthentication" class="mb-6" action="{{route('register.post')}}" method="POST">
          @csrf

            <div class="mb-6">
              <!-- <label for="username" class="form-label">الاسم الرباعي</label> -->
              <input type="text" class="form-control" value="{{old('username')}}" id="username" name="username" placeholder=" ادخل الاسم الرباعي " autofocus>
            </div>
            <div class="mb-6">
              <!-- <label for="phone" class="form-label"> رقم الجوال</label> -->
              <input type="number" class="form-control" value="{{old('phone')}}" id="phone" name="phone" placeholder="أدخل رقم الجوال">
            </div>
            <div class="mb-6">
              <!-- <label for="email" class="form-label">البريد الالكتروني</label> -->
              <input type="text" class="form-control" value="{{old('email')}}" id="email" name="email" placeholder="أدخل البريد الالكتروني">
            </div>

            <div class="mb-6">
              <label for="data-of-birth" class="form-label">تاريخ المبلاد</label>
              <div class="col-md-12">
                <input class="form-control" type="date" value="{{old('dateofbirth')}}" name="dateofbirth"  id="dateofbirth" placeholder="ادخل تاريخ المبلاد" />
              </div>
            </div>
            <div class="mb-6">
              <!-- <label for="id-card" class="form-label"> رقم الهوية</label> -->
              <input type="text" class="form-control" value="{{old('idcard')}}" id="idcard" name="idcard" placeholder="أدخل رقم الهوية">
            </div>
            <div class="mb-6 form-password-toggle" dir="rtl">
              <label class="form-label" for="password" style="float: right;">كلمة المرور</label>
              <div class="input-group  flex-row-reverse">
                <input
                  type="password"
                  id="password"
                  class="form-control"
                  name="password"
                  placeholder="••••••••••"
                  aria-describedby="password"
                  style="text-align: right;"
                />
                <span class="input-group-text cursor-pointer">
                  <i class="bx bx-hide"></i>
                </span>
              </div>
            </div>
            <div class="my-8">
              <div class="form-check mb-0 ms-2">
                <input class="form-check-input" type="checkbox" id="terms-conditions" name="terms">
                <label class="form-check-label" for="terms-conditions">
                  اوافق على
                  <a href="javascript:void(0);"> الشروط والاحكام </a>
                </label>
              </div>
            </div>
            <button class="btn btn-primary d-grid w-100">
              تسجيل
            </button>
            @if ($errors->any())
              <div class="alert alert-danger">
                <ul class="mb-0">
                  @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                  @endforeach
                </ul>
              </div>
            @endif
          </form>

          <p class="text-center">
            <span> لدي حساب بالفعل </span>
            <a href="{{url('auth/login')}}">
              <span> تسجيل الدخول </span>
            </a>
          </p>
        </div>
      </div>
      <!-- Register Card -->
    </div>
  </div>
</div>
@endsection
