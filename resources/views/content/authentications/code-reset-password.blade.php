@extends('layouts.blankLayout')

@section('title', 'التحقق من رمز التفعيل')

@section('page-style')
@vite([
  'resources/assets/vendor/scss/pages/page-auth.scss'
])
@endsection

@section('content')
<div class="container-xxl">
  <div class="authentication-wrapper authentication-basic container-p-y">
    <div class="authentication-inner">
      <div class="card px-sm-6 px-0">
        <div class="card-body">
          <div class="app-brand justify-content-center mb-6">
            <a href="/" class="app-brand-link gap-2">
              <span class="app-brand-text demo text-heading fw-bold">{{config('variables.templateName')}}</span>
            </a>
          </div>
          <h4 class="mb-1">أدخل رمز التفعيل</h4>
          <p class="mb-6">تم إرسال رمز التفعيل إلى رقم هاتفك.</p>
          <form id="otp" class="mb-6" action="{{route('verify.reset.code')}}" method="POST">
            @csrf
             <div class="mb-6">
              <input type="text" class="form-control text-center" name="identity" maxlength="10"   placeholder="ادخل رقم الهوية" required autofocus>
            </div>
            <div class="mb-6">
              <input type="text" class="form-control text-center" name="code" maxlength="6" minlength="6" pattern="[0-9]{6}" placeholder="ادخل رمز التحقق" required autofocus>
            </div>

            <button class="btn btn-primary d-grid w-100" type="submit">تأكيد</button>
            @if ($errors->any())
              <div class="alert alert-danger mt-3">
                <ul class="mb-0">
                  @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                  @endforeach
                </ul>
              </div>
            @endif
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
