@extends('layouts/blankLayout')

@section('title', 'Login Basic - Pages')

@section('page-style')
@vite([
  'resources/assets/vendor/scss/pages/page-auth.scss'
])
@endsection

@section('content')
<div class="container-xxl">
  <div class="authentication-wrapper authentication-basic container-p-y">
    <div class="authentication-inner">
      <!-- Register -->
      <div class="card px-sm-6 px-0">
        <div class="card-body">
          <!-- Logo -->
          <div class="app-brand justify-content-center">
            <a href="{{url('/')}}" class="app-brand-link gap-2">
              <span class="app-brand-logo demo">@include('_partials.macros',["width"=>25,"withbg"=>'var(--bs-primary)'])</span>
              <span class="app-brand-text demo text-heading fw-bold">{{config('variables.templateName')}}</span>
            </a>
          </div>
          <!-- /Logo -->
          <h4 class="mb-1">مرحبا بك في  {{config('variables.templateName')}}! 👋</h4>
          <p class="mb-6"> يرجى تسجيل الدخول إلى حسابك وابدأ رحلتك مع التكافل </p>

          <form id="formAuthentication" class="mb-6" action="{{route('login.post')}}" method="POST">
          @csrf

            <div class="mb-6">
              <label for="userID" class="form-label">رقم االهوية</label>
              <input type="text" class="form-control" id="userID" name="identity" placeholder="ادخل رقم الهوية" autofocus>
            </div>
            <div class="mb-6 form-password-toggle" dir="rtl">
              <label class="form-label" for="password" style="float: right;">كلمة المرور</label>
              <div class="input-group  flex-row-reverse">
                <input
                require
                  type="password"
                  id="password"
                  class="form-control"
                  name="password"
                  placeholder="••••••••••"
                  aria-describedby="password"
                  style="text-align: right;"
                />
                <span class="input-group-text cursor-pointer">
                  <i class="bx bx-hide"></i>
                </span>
              </div>
            </div>
            <div class="mb-8">
              <div class="d-flex justify-content-between mt-8">
                <div class="form-check mb-0 ms-2">
                  <input class="form-check-input" type="checkbox" id="remember-me">
                  <label class="form-check-label" for="remember-me">
                     تذكرني
                  </label>
                </div>
                <a href="{{url('auth/forgot-password-basic')}}">
                  <span>هل نسيت كلمة المرور؟ </span>
                </a>
              </div>
            </div>
            <div class="mb-6">
              <button class="btn btn-primary d-grid w-100" type="submit">الدخول</button>
            </div>
            @if ($errors->any())
              <div class="alert alert-danger">
                <ul class="mb-0">
                  @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                  @endforeach
                </ul>
              </div>
            @endif
          </form>

          <p class="text-center">
            <span> جديد في التكافل للتأمين؟ </span>
            <a href="{{url('auth/register')}}">
              <span> قم بإنشاء الحساب الخاص بك  </span>
            </a>
          </p>
        </div>
      </div>
    </div>
    <!-- /Register -->
  </div>
</div>
@endsection
