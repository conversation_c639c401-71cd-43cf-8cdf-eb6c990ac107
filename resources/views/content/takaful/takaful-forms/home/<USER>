@extends('layouts.wordpressLayout')

@section('title', 'حاسبة قسط تأمين المنزل')

{{-- jQuery CDN --}}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
{{-- Boxicons CSS --}}
<link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
{{-- Modern Style CSS --}}
<link href="{{ asset('assets/css/modern-style.css') }}" rel="stylesheet">
<style>
   .document-upload-area {
        border: 2px dashed #e2e8f0;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        background: #f8fafc;
        cursor: pointer;
    }
    .document-upload-area:hover {
        border-color: #696cff;
        background: #fff;
    }
    .document-upload-area i {
        font-size: 2rem;
        color: #696cff;
        margin-bottom: 1rem;
    }
  </style>
@section('content')
<div class="insurance-form-container rtl">
  <div class="form-header">
    <h4>حاسبة قسط تأمين المنزل</h4>
    <p class="text-muted">
      توفر شركة التكافل أداة ذكية وسهلة الاستخدام لحساب قسط تأمين المنزل بدقة وسرعة. تتيح لك الحاسبة إدخال معلومات العقار الأساسية مثل نوع البناء، موقع المنزل، المساحة، قيمة المحتويات، ونوع التغطية المطلوبة، لتحصل فورًا على تقدير تقريبي لقيمة القسط التأميني.
    </p>
  </div>

  <form id="car-calc-form" action="{{ route('insurance-orders.store') }}" method="POST" enctype="multipart/form-data">
    @csrf
    
    {{-- معلومات المنزل الأساسية --}}
    <div class="form-section">
      <div class="card-body">
        <div class="section-title">
          <i class='bx bx-home-alt'></i>
          <span>معلومات المنزل الأساسية</span>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label">نوع المنزل</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-building-house'></i></span>
                <select class="form-select" id='p_home_type' name="details[p_home_type]">
                  <option value="">اختر النوع</option>
                  @foreach($hometype as $type)
                    <option value="{{$type['home_build_code']}}">{{$type['home_buid_aname']}}</option>
                  @endforeach
                </select>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label">ملكية المنزل</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-key'></i></span>
                <select class="form-select" id='p_home_cat' name="details[p_home_cat]">
                  <option value="">اختر النوع</option>
                  @foreach($ownershiptype as $type)
                    <option value="{{$type['home_own_type']}}">{{$type['home_own_aname']}}</option>
                  @endforeach
                </select>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label">نوع العملة</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-money'></i></span>
                <select class="form-select" name="cointype">
                  <option value="">اختر النوع</option>
                  <option value="1">شيكل</option>
                  <option value="2">دولار</option>
                </select>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label">قيمة البناء</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-building'></i></span>
                <input type="number" id="p_build_si" name="details[p_build_si]" class="form-control" placeholder="أدخل قيمة البناء" />
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label">قيمة المحتويات</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-cabinet'></i></span>
                <input type="number" id="p_content_si" name="details[p_content_si]" class="form-control" placeholder="أدخل قيمة المحتويات" />
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label">المنطقة</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-map'></i></span>
                <select class="form-select" name="details[location]">
                  <option value="">اختر المنطقة</option>
                  @foreach($locations as $location)
                    <option value="{{$location['travel_to_code']}}">{{$location['travel_to_sname_a']}}</option>
                  @endforeach
                </select>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label">تاريخ بداية التأمين</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-calendar'></i></span>
                <input type="date" name="details[p_ins_start_dt]" id="p_ins_start_dt" class="form-control">
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label">تاريخ نهاية التأمين</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-calendar-x'></i></span>
                <input disabled type="date" name="details[p_ins_end_dt]" id="p_ins_end_dt" class="form-control" readonly>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label">القسط</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-dollar'></i></span>
                <input disabled type="number" id="installment" name="total_cost" class="form-control" />
              </div>
            </div>
          </div>
        </div>

        <input type="hidden" id="type_id" name="type_id" value="2">
        <input type="hidden" name="details[p_prem]">
        <input type="hidden" name="details[p_fees]">
        <input type="hidden" name="details[p_min_prem]">
        <input type="hidden" name="details[p_perc]">
        <input type="hidden" name="total_cost">

        <div class="text-center mt-4">
          <button type="button" class="btn btn-primary" id="calc-btn">
            <i class='bx bx-calculator'></i>
            احسب
          </button>
        </div>

        <div id="responseMessage" class="mt-3"></div>
      </div>
    </div>

    {{-- بيانات العميل --}}
    <div class="form-section">
      <div class="card-body">
        <div class="section-title">
          <i class='bx bx-user'></i>
          <span>بيانات العميل</span>
        </div>

        <div class="row">
          @php
          $userFields = [
            ['label' => 'اسم المؤمن له', 'id' => 'customer_name', 'name' => 'insured_full_name', 'value' => auth()->user()->fullname],
            ['label' => 'رقم الهوية', 'id' => 'customer_id', 'name' => 'insured_identity', 'value' => auth()->user()->identity],
            ['label' => 'تاريخ الميلاد', 'id' => 'customer_dob', 'name' => 'insured_date_of_birth', 'value' => auth()->user()->date_of_birth],
            ['label' => 'البريد الإلكتروني', 'id' => 'customer_email', 'name' => 'insured_email', 'value' => auth()->user()->email],
            ['label' => 'رقم الهاتف', 'id' => 'customer_phone', 'name' => 'insured_phone', 'value' => auth()->user()->mobile],
          ];
          $questions = [
                          [
                            'label' => 'هل لديك تأمين سابق؟',
                            'name' => 'has_previous_insurance',
                            'type' => 'select',
                            'options' => ['نعم', 'لا'],
                            'id' => 'has_previous_insurance'
                          ],
                          [
                            'label' => 'ما نوع البناء؟',
                            'name' => 'building_type',
                            'type' => 'text',
                            'id' => 'building_type'
                          ],
                          [
                            'label' => 'هل تم بناء المنزل قبل عام 2000؟',
                            'name' => 'built_before_2000',
                            'type' => 'radio',
                            'options' => ['نعم', 'لا'],
                            'id' => 'built_before_2000'
                          ]
                        ];
          @endphp

          @foreach($userFields as $field)
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label" for="{{ $field['id'] }}">{{ $field['label'] }}</label>
              <div class="input-group">
                <span class="input-group-text">
                  @switch($field['name'])
                    @case('insured_full_name')
                      <i class='bx bx-user'></i>
                      @break
                    @case('insured_identity')
                      <i class='bx bx-id-card'></i>
                      @break
                    @case('insured_date_of_birth')
                      <i class='bx bx-calendar'></i>
                      @break
                    @case('insured_email')
                      <i class='bx bx-envelope'></i>
                      @break
                    @case('insured_phone')
                      <i class='bx bx-phone'></i>
                      @break
                  @endswitch
                </span>
                <input type="text" 
                       name="details[{{ $field['name'] }}]" 
                       id="{{ $field['id'] }}" 
                       class="form-control" 
                       value="{{ $field['value'] }}"
                       @if($field['name'] == 'insured_date_of_birth') type="date" @endif>
              </div>
            </div>
          </div>
          @endforeach
        </div>
      </div>
    </div>

    {{-- معلومات البناء --}}
    <div class="form-section">
      <div class="card-body">
        <div class="section-title">
          <i class='bx bx-building-house'></i>
          <span>معلومات البناء</span>
        </div>

        <div class="row">
          {{-- نوع البناء --}}
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label" for="building_type">نوع البناء</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-buildings'></i></span>
                <select name="details[structure_types]" id="structure_types" class="form-select">
                  <option value="select">اختر</option>
                  @foreach($archtype as $type)
                    <option value="{{$type['home_arch_type']}}">{{$type['home_arch_aname']}}</option>
                  @endforeach 
                </select>
              </div>
            </div>
          </div>

          {{-- العنوان --}}
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label" for="address">العنوان</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-map-pin'></i></span>
                <input type="text" name="details[address]" id="address" class="form-control" placeholder="أدخل العنوان">
              </div>
            </div>
          </div>

          {{-- المدينة --}}
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label" for="city">المدينة</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-city'></i></span>
                <select name="details[city]" id="city" class="form-select">
                  <option value="select">اختر</option>
                  @foreach($locations as $location)
                    <option value="{{$location['travel_to_code']}}">{{$location['travel_to_sname_a']}}</option>
                  @endforeach
                </select>
              </div>
            </div>
          </div>

          {{-- الشارع --}}
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label" for="street">الشارع</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-street-view'></i></span>
                <input type="text" name="details[street]" id="street" class="form-control" placeholder="أدخل اسم الشارع">
              </div>
            </div>
          </div>

          {{-- رقم المبنى --}}
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label" for="building_number">رقم المبنى</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-hash'></i></span>
                <input type="text" name="details[building_number]" id="building_number" class="form-control" placeholder="أدخل رقم المبنى">
              </div>
            </div>
          </div>

          {{-- ارضية البناء --}}
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label" for="floor_type">ارضية البناء</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-layer'></i></span>
                <select name="details[building_floor_type]" id="floor_type" class="form-select">
                  <option value="select">اختر</option>
                  @foreach($floortype as $type)
                    <option value="{{$type['home_floor_code']}}">{{$type['home_floor_aname']}}</option>
                  @endforeach
                </select>
              </div>
            </div>
          </div>

          {{-- سقفية البناء --}}
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label" for="roof_type">سقفية البناء</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-home-smile'></i></span>
                <select name="details[building_roof_type]" id="roof_type" class="form-select">
                  <option value="select">اختر</option>
                  @foreach($ceiling as $type)
                    <option value="{{$type['home_ceiling_code']}}">{{$type['home_ceiling_aname']}}</option>
                  @endforeach
                </select>
              </div>
            </div>
          </div>

          {{-- جدرانه --}}
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label" for="wall_type">جدرانه</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-wall'></i></span>
                <select name="details[building_wall_type]" id="wall_type" class="form-select">
                  <option value="select">اختر</option>
                  @foreach($walltype as $wall)
                    <option value="{{$wall['home_wall_code']}}">{{$wall['home_wall_aname']}}</option>
                  @endforeach
                </select>
              </div>
            </div>
          </div>

          {{-- مساحة البناء --}}
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label" for="building_area">مساحة البناء (م²)</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-area'></i></span>
                <input type="number" name="details[building_area]" id="building_area" class="form-control" placeholder="أدخل مساحة البناء">
              </div>
            </div>
          </div>

          {{-- مساحة الأرض --}}
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label" for="land_area">مساحة الأرض (م²)</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-landscape'></i></span>
                <input type="number" name="details[land_area]" id="land_area" class="form-control" placeholder="أدخل مساحة الأرض">
              </div>
            </div>
          </div>

          {{-- هل البناء مرهون؟ --}}
          <div class="col-md-6">
            <div class="mb-3">
              <label class="form-label" for="is_mortgaged">هل البناء مرهون؟</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-lock-alt'></i></span>
                <select name="details[is_mortgaged]" id="is_mortgaged" class="form-select">
                  <option value="no">لا</option>
                  <option value="yes">نعم</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {{-- تفاصيل الرهن --}}
        <div id="mortgage_details" style="display: none;">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label" for="mortgage_entity">اسم الجهة المرهون لها</label>
                <div class="input-group">
                  <span class="input-group-text"><i class='bx bx-bank'></i></span>
                  <input type="text" name="details[mortgage_holder]" id="mortgage_entity" class="form-control" placeholder="أدخل اسم الجهة">
                </div>
              </div>
            </div>

            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label" for="mortgage_end_date">تاريخ انتهاء الرهن</label>
                <div class="input-group">
                  <span class="input-group-text"><i class='bx bx-calendar-x'></i></span>
                  <input type="date" name="details[mortgage_end_date]" id="mortgage_end_date" class="form-control">
                </div>
              </div>
            </div>
          </div>
        </div>

        {{-- ملاحظات --}}
        <div class="row">
          <div class="col-12">
            <div class="mb-3">
              <label class="form-label" for="notes">ملاحظات أخرى</label>
              <div class="input-group">
                <span class="input-group-text"><i class='bx bx-note'></i></span>
                <textarea name="details[notes]" id="notes" class="form-control" maxlength="4000" rows="5" placeholder="أدخل أي ملاحظات إضافية"></textarea>
              </div>
            </div>
          </div>
        </div>

        {{-- صور المبنى --}}
          <div class="row">
            <div class="col-12">
                <label class="form-label">المستندات المطلوبة</label>
                <div class="document-upload-area" onclick="document.getElementById('building_images').click()">
                    <i class="bx bx-upload"></i>
                    <p class="upload-text">اسحب وأفلت الملفات هنا أو انقر للتحميل</p>
                    <small class="text-muted">يمكنك رفع عدة صور في نفس الوقت</small>
                    <input type="file" id="building_images" name="images[]" class="form-control d-none" accept="image/*" multiple>
                </div>
                <div id="preview-container" class="mt-3 row g-2"></div>
            </div>
        </div>
        <!-- <div class="row">
          <div class="col-12">
            <div class="mb-3">
              <label class="form-label" for="building_images">صور المبنى</label>
              <div class="file-upload">
                <label class="file-upload-label" for="building_images">
                  <i class='bx bx-upload'></i>
                  <span>اختر الصور أو اسحبها هنا</span>
                </label>
                <input type="file" name="images[]" id="building_images" class="form-control" multiple>
              </div>
            </div>
          </div>
        </div> -->

        {{-- الأسئلة --}}
        <div class="row mt-4">
          <div class="col-12">
            <div class="section-title">
              <i class='bx bx-question-mark'></i>
              <span>الأسئلة</span>
            </div>
            
            @foreach($qustions as $q)
              <div class="mb-3">
                <label class="form-label d-block">{{ $q['question_adesc'] }}</label>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" name="questions[{{ $q['question_id'] }}]" id="{{ $q['question_id'] }}_yes" value="yes">
                  <label class="form-check-label" for="{{ $q['question_id'] }}_yes">نعم</label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" name="questions[{{ $q['question_id'] }}]" id="{{ $q['question_id'] }}_no" value="no">
                  <label class="form-check-label" for="{{ $q['question_id'] }}_no">لا</label>
                </div>
              </div>
            @endforeach
          </div>
        </div>

        {{-- زر الإرسال --}}
        <div class="text-center mt-4">
          <button type="submit" class="btn btn-primary">
            <i class='bx bx-send'></i>
            إرسال الطلب
          </button>
        </div>
      </div>
    </div>
  </form>
</div>
@endsection
@section('scripts')
<script>
   document.getElementById('building_images').addEventListener('change', function(e) {
    const container = document.getElementById('preview-container');
    container.innerHTML = '';
    
    Array.from(e.target.files).forEach((file, index) => {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.createElement('div');
            preview.className = 'col-md-3 col-sm-4 col-6';
            preview.innerHTML = `
                <div class="position-relative">
                    <img src="${e.target.result}" class="img-fluid rounded" style="height: 150px; width: 100%; object-fit: cover;">
                    <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1" onclick="removePreview(this, ${index})">
                        <i class="bx bx-x"></i>
                    </button>
                </div>
            `;
            container.appendChild(preview);
        }
        reader.readAsDataURL(file);
    });
});
function removePreview(button, index) {
    const input = document.getElementById('document-upload');
    const container = document.getElementById('preview-container');
    
    // Remove the preview
    button.closest('.col-md-3').remove();
    
    // Create a new FileList without the removed file
    const dt = new DataTransfer();
    Array.from(input.files).forEach((file, i) => {
        if(i !== index) dt.items.add(file);
    });
    input.files = dt.files;
}
   $(document).ready(function() {
    $('#calc-btn').click(function(event) {
      event.preventDefault();

      console.log('test'); // <-- fixed here

      var formData = {
                    p_home_type:$('#p_home_type').val(),
                    p_home_cat:$('#p_home_cat').val(),
                    p_content_si:$('#p_content_si').val(),
                    p_build_si:$('#p_build_si').val(),
                   p_ins_start_dt:$('#p_ins_start_dt').val(),
                    p_ins_end_dt:$('#p_ins_end_dt').val(),
        access_token: $('meta[name="csrf-token"]').attr('content')
      };

      $.ajax({
        url: 'http://82.213.0.2:8000/home-comp-prem',
        type: 'GET',
        data: formData,
        success: function(response) {
          console.log(response);
           $('input[name="details[p_prem]"]').val(response.prem);
           $('input[name="details[p_min_prem]"]').val(response.min_prem);
           $('input[name="details[p_perc]"]').val(response.perc);
           $('input[name="p_fees"]').val(response.fees);
          const homeCatVal = Number($('#p_build_si').val());
          const buildSiVal = Number($('#p_content_si').val() || 0);
          const suminsu = (homeCatVal + buildSiVal) * response.perc;
          if(response.min_prem > suminsu){
            $('#installment').val(response.min_prem)
            $('input[name="total_cost"]').val(response.min_prem)
          }else{
            $('#installment').val(suminsu)
            ('input[name="total_cost"]').val(suminsu)

          }
  
        },
        error: function(xhr, status, error) {
          console.log(xhr.responseText); // this is better than `response` which is undefined here
          $('#responseMessage').html('<p>Error: ' + error + '</p>');
        }
      });
    });
  });

  const startDateInput = document.getElementById('p_ins_start_dt');
const endDateInput = document.getElementById('p_ins_end_dt');
const is_mortgaged = document.getElementById('is_mortgaged');
const mortgagedDetails = document.getElementById('mortgage_details');

function calculateEndDate() {
  const startDate = new Date(startDateInput.value);
  if (!isNaN(startDate)) {
    const endDate = new Date(startDate);
    endDate.setFullYear(endDate.getFullYear() + 1);
    endDateInput.value = endDate.toISOString().split('T')[0];
  }
}
startDateInput.addEventListener("change", calculateEndDate);
//hide is_mortgaged
is_mortgaged.addEventListener("change", function() {
  const selectedVal = is_mortgaged.value;
if(selectedVal=='yes'){
  mortgagedDetails.style.display = "block";
}else{
  mortgagedDetails.style.display = "none";

}
})
</script>
@endsection
