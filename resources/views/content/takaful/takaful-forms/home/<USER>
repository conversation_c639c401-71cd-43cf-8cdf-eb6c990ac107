@extends('layouts.wordpressLayout')

@section('title', 'حاسبة قسط تأمين المنزل')

@section('content')
<div class="row" style="">
  <h5 class="mb-0">حاسبة قسط تأمين المنزل</h5>
  <p class="text-muted float-end">
  توفر شركة التكافل أداة ذكية وسهلة الاستخدام لحساب قسط تأمين المنزل بدقة وسرعة. تتيح لك الحاسبة إدخال معلومات العقار الأساسية مثل نوع البناء، موقع المنزل، المساحة، قيمة المحتويات، ونوع التغطية المطلوبة، لتحصل فورًا على تقدير تقريبي لقيمة القسط التأميني.

تهدف هذه الخدمة إلى مساعدة أصحاب المنازل على التخطيط المالي بشكل أفضل ومعرفة التكاليف المتوقعة قبل شراء بوليصة تأمين المنزل، بكل شفافية وسلاسة، مما يضمن راحة بالك واستقرار منزلك.
</p>

  <form id="car-calc-form" action="{{ route('insurance-orders.store') }}" method="POST">
    @csrf

    {{-- بيانات الرحلة --}}
    <div class="col-xxl">
      <div class="card mb-6">
        <div class="card-body">
        <div class="row mb-3">
            <label class="col-sm-3 col-form-label">نوع المنزل</label>
            <div class="col-sm-9">
              <select class="form-select" name="details[p_home_type]">
              <option value="">اختر النوع</option>
                  @foreach($hometype as $type)
                      <option value="{{ $type['home_build_code'] }}"
                          {{ $order?->HomeInsuranceDetails?->p_home_type == $type['home_build_code'] ? 'selected' : '' }}>
                          {{ $type['home_buid_aname'] }}
                      </option>
                @endforeach
              </select>
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-3 col-form-label">ملكية المنزل</label>
            <div class="col-sm-9">
              <select class="form-select" name="details[p_home_cat]">
                  <option value="">اختر النوع</option>
                @foreach($ownershiptype as $type)
                <option value="{{$type['home_own_type']}}"  {{ $order?->HomeInsuranceDetails?->p_home_cat == $type['home_own_type'] ? 'selected' : '' }} >{{$type['home_own_aname']}}</option>
                @endforeach
              </select>
              </select>
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-3 col-form-label">توع العملة</label>
            <div class="col-sm-9">
              <select class="form-select" name="cointype">
                <option value="">اختر النوع</option>
                <option value="">شيكل</option>
                <option value="">دولار</option>
              </select>
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-3 col-form-label" for="constructionvalue">قيمة البناء</label>
            <div class="col-sm-9">
              <input type="number" id="constructionvalue" name="details[p_build_si]" class="form-control" value="{{$order?->HomeInsuranceDetails?->p_build_si}}" />
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-3 col-form-label" for="contentsvalue"></label>
            <div class="col-sm-9">
              <input type="number" id="contentsvalue" name="details[p_content_si]" class="form-control" value="{{$order?->HomeInsuranceDetails?->p_content_si}}"/>
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-3 col-form-label">المنطقة</label>
            <div class="col-sm-9">
              <select class="form-select" name="details[location]">
                  <option value="">اختر النوع</option>
                @foreach($locations as $location)
                <option value="{{$location['travel_to_code']}}" {{ $order?->HomeInsuranceDetails?->location == $location['travel_to_code'] ? 'selected' : '' }}>{{$location['travel_to_sname_a']}}</option>
                @endforeach
              </select>
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-3 col-form-label" for="installment">القسط</label>
            <div class="col-sm-9">
              <input disabled type="number" id="installment" name="installment" class="form-control" />
            </div>
          </div>
          <div style="display: none;" class="row mb-3">
            <label class="col-sm-3 col-form-label" for="minimum">الحد الادنى</label>
            <div class="col-sm-9">
              <input disabled type="number" id="minimum" name="minimum" class="form-control" />
            </div>
          </div>
          <input type="hidden" id="type_id" name="type_id" value="2">

          {{-- زر احسب --}}
          <div class="row justify-content-end">
            <div class="col-sm-10">
              <button type="button" class="btn btn-primary" id="calc-btn">احسب</button>
            </div>
          </div>

          <div id="responseMessage" class="mt-3 text-success"></div>
        </div>
      </div>
    </div>
    {{-- بيانات العميل --}}
    <div class="col-xxl">
      <div class="card shadow-sm">
        <div class="card-body text-center">
          <h5 class="card-title mb-4">بيانات العميل</h5>
          @php
          $userFields = [
            ['label' => 'اسم المؤمن له', 'id' => 'customer_name', 'name' => 'insured_full_name', 'value' => auth()->user()->fullname],
            ['label' => 'رقم الهوية', 'id' => 'customer_id', 'name' => 'insured_identity', 'value' => auth()->user()->identity],
            ['label' => 'تاريخ الميلاد', 'id' => 'customer_dob', 'name' => 'insured_date_of_birth', 'value' => auth()->user()->date_of_birth],
            ['label' => 'البريد الإلكتروني', 'id' => 'customer_email', 'name' => 'insured_email', 'value' => auth()->user()->email],
            ['label' => 'رقم الهاتف', 'id' => 'customer_phone', 'name' => 'insured_phone', 'value' => auth()->user()->mobile],
          ];
          @endphp

          @foreach($userFields as $field)
          <div class="row mb-3">
            <label class="col-sm-3 col-form-label" for="{{ $field['id'] }}">{{ $field['label'] }}</label>
            <div class="col-sm-9">
              <input type="text" name="details[{{ $field['name'] }}]" id="{{ $field['id'] }}" class="form-control" value="{{ $field['value'] }}">
            </div>
          </div>
          @endforeach
        </div>
      </div>
    </div>
    <div class="col-xxl">
      <div class="card shadow-sm">
         <div class="card-body text-center">
               <h4>معلومات البناء</h4>

                <!-- نوع البناء -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label" for="building_type">نوع البناء</label>
                    <div class="col-sm-9">
                        <select name="building_type" id="details[structure_types]" class="form-control">
                        <option value="select">اختر</option>
                        @foreach($archtype as $type)
                        <option value="{{$type['home_arch_type']}}" {{ $order?->HomeInsuranceDetails?->structure_types == $type['home_arch_type'] ? 'selected' : '' }}>{{$type['home_arch_aname']}}</option>
                         @endforeach
                        </select>
                    </div>
                </div>

                <!-- تاريخ بداية التأمين -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label" for="insurance_start">تاريخ بداية التأمين</label>
                    <div class="col-sm-9">
                        <input type="date" name="details[p_ins_start_dt]" id="insurance_start" class="form-control" value="{{$order?->HomeInsuranceDetails?->p_ins_start_dt}}" >
                    </div>
                </div>

                <!-- تاريخ نهاية التأمين -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label" for="insurance_end">تاريخ نهاية التأمين</label>
                    <div class="col-sm-9">
                        <input type="date" name="details[p_ins_end_dt]" id="insurance_end" class="form-control" readonly>
                    </div>
                </div>

                <!-- العنوان -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label" for="address">العنوان</label>
                    <div class="col-sm-9">
                        <input type="text" name="details[address]" id="address" class="form-control" value="{{$order?->HomeInsuranceDetails?->address}}" >
                    </div>
                </div>

                <!-- المدينة -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label" for="city">المدينة</label>
                    <div class="col-sm-9">
                        <select name="city" id="city" class="form-control">
                              <option value="select">اختر</option>
                             @foreach($locations as $location)
                              <option value="{{$location['travel_to_code']}}" {{ $order?->HomeInsuranceDetails?->city == $location['travel_to_code'] ? 'selected' : '' }}>{{$location['travel_to_sname_a']}}</option>
                              @endforeach
                        </select>
                    </div>
                </div>

                <!-- الشارع -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label" for="street">الشارع</label>
                    <div class="col-sm-9">
                        <input type="text" name="details[street]" id="street" class="form-control" value="{{$order?->HomeInsuranceDetails?->street}}">
                    </div>
                </div>

                <!-- رقم المبنى -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label" for="building_number">رقم المبنى</label>
                    <div class="col-sm-9">
                        <input type="text" name="details[building_number]" id="building_number" class="form-control" value="{{$order?->HomeInsuranceDetails?->building_number}}">
                    </div>
                </div>

                <!-- ارضية البناء -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label" for="floor_type">ارضية البناء</label>
                    <div class="col-sm-9">
                        <select name="details[building_floor_type]" id="floor_type" class="form-control">
                         @foreach($floortype as $type)
                        <option value="{{$type['home_floor_code']}}" {{ $order?->HomeInsuranceDetails?->building_floor_type == $type['home_floor_code'] ? 'selected' : '' }}>{{$type['home_floor_aname']}}</option>
                        @endforeach
                        </select>
                    </div>
                </div>
                <!-- سقفية البناء -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label" for="roof_type">سقفية البناء</label>
                    <div class="col-sm-9">
                        <select name="details[building_roof_type]" id="roof_type" class="form-control">
                         <option value="select">اختر</option>
                        @foreach($ceiling as $type)
                        <option value="{{$type['home_ceiling_code']}}" {{ $order?->HomeInsuranceDetails?->building_roof_type == $type['home_ceiling_code'] ? 'selected' : '' }}>{{$type['home_ceiling_aname']}}</option>
                        @endforeach
                        </select>
                    </div>
                </div>

                <!-- جدرانه -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label" for="wall_type">جدرانه</label>
                    <div class="col-sm-9">
                        <select name="details[building_wall_type]" id="wall_type" class="form-control">
                         <option value="select">اختر</option>
                        @foreach($walltype as $wall)
                        <option value="{{$wall['home_wall_code']}}" {{ $order?->HomeInsuranceDetails?->building_wall_type == $wall['home_wall_code'] ? 'selected' : '' }}>{{$wall['home_wall_aname']}}</option>
                        @endforeach
                        </select>
                    </div>
                </div>

                <!-- مساحة البناء -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label" for="building_area">مساحة البناء (م²)</label>
                    <div class="col-sm-9">
                        <input type="number" name="details[building_area]" id="building_area" class="form-control" value="{{$order?->HomeInsuranceDetails?->building_area}}">
                    </div>
                </div>

                <!-- مساحة الأرض -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label" for="land_area">مساحة الأرض (م²)</label>
                    <div class="col-sm-9">
                        <input type="number" name="details[land_area]" id="land_area" class="form-control" value="{{$order?->HomeInsuranceDetails?->land_area}}">
                    </div>
                </div>

                <!-- الحد الأدنى (المطلوب إضافته) -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label" for="minimum">الحد الأدنى</label>
                    <div class="col-sm-9">
                        <input disabled type="number" id="minimum" name="minimum" class="form-control" />
                    </div>
                </div>

                <!-- هل البناء مرهون؟ -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label" for="is_mortgaged">هل البناء مرهون؟</label>
                    <div class="col-sm-9">
                        <select name="is_mortgaged" id="is_mortgaged" class="form-control">
                            <option value="no">لا</option>
                            <option value="yes">نعم</option>
                        </select>
                    </div>
                </div>

                <!-- تفاصيل الرهن (تظهر إذا مرهون) -->
                <div id="mortgage_details" style="display: none;">
                    <div class="row mb-3">
                        <label class="col-sm-3 col-form-label" for="mortgage_entity">اسم الجهة المرهون لها</label>
                        <div class="col-sm-9">
                            <input type="text" name="details[mortgage_holder]" id="mortgage_entity" class="form-control" value="{{$order?->HomeInsuranceDetails?->mortgage_holder}}">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label class="col-sm-3 col-form-label" for="mortgage_end_date">تاريخ انتهاء الرهن</label>
                        <div class="col-sm-9">
                            <input type="date" name="details[mortgage_end_date]" id="mortgage_end_date" class="form-control" value="{{$order?->HomeInsuranceDetails?->mortgage_end_date}}">
                        </div>
                    </div>
                </div>

                <!-- ملاحظات -->
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label" for="notes">ملاحظات أخرى</label>
                    <div class="col-sm-9">
                        <textarea name="details[notes]" id="notes" class="form-control" maxlength="4000" rows="5"  value="{{$order?->HomeInsuranceDetails?->mortgage_end_date}}"></textarea>
                    </div>
                </div>
                </div>
                 <div class="card-body ">
                        <h5 class="card-title mb-4 text-center">الأسئلة</h5>

                        @foreach ($order?->HomeInsuranceDetails?->qustionsOreder as $item)
                            <div class="mb-3 d-flex align-items-center">
                                <label class="form-label mb-0 me-3" style="min-width: 200px;">
                                    {{ $item->question_text ?? 'السؤال غير متوفر' }}
                                </label>

                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio"
                                          name="answer_{{ $item->question_id }}"
                                          value="yes"
                                          {{ $item->answer === 'yes' ? 'checked' : '' }}
                                          disabled>
                                    <label class="form-check-label">نعم</label>
                                </div>

                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio"
                                          name="answer_{{ $item->question_id }}"
                                          value="no"
                                          {{ $item->answer === 'no' ? 'checked' : '' }}
                                          disabled>
                                    <label class="form-check-label">لا</label>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    @if(isset($order?->HomeInsuranceDetails?->imagesOreder))
                      <div class="row">
                          @foreach($order?->HomeInsuranceDetails?->imagesOreder as $image)
                              <div class="col-md-3 mb-3">
                                  <img src="{{ showImage($image->image_path) }}" alt="Home Image" class="img-fluid rounded" />
                              </div>
                          @endforeach
                      </div>
                    @endif
                <!-- زر الإرسال -->
                <div class="row mb-3" style="display:none;">
                    <div class="col-sm-12 text-end">
                        <button type="submit" class="btn btn-primary">إرسال</button>
                    </div>
                </div>

        </div>
      </div>
    </div>
        </div>


</div>
@endsection
@section('scripts')
<script>

document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('input, button, select, textarea').forEach(el => el.disabled = true);})
    </script>
@endsection
