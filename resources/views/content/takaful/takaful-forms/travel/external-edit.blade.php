@extends('layouts.wordpressLayout')

@section('title', 'حاسبة قسط تأمين السفر')

@section('page-style')
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/airbnb.css">
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

<style>
    .form-section {
        background: #fff;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        margin-bottom: 2rem;
    }
    .form-section:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    .section-title {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #edf2f7;
        position: relative;
    }
    .section-title:after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 50px;
        height: 2px;
        background: ##003D4C;
    }
    .form-label {
        font-weight: 500;
        color: #4a5568;
        margin-bottom: 0.5rem;
    }
    .form-control, .form-select {
        border-radius: 10px;
        border: 1.5px solid #e2e8f0;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
        background-color: #f8fafc;
    }
    .form-control:focus, .form-select:focus {
        border-color: ##003D4C;
        box-shadow: 0 0 0 3px rgba(105, 108, 255, 0.1);
        background-color: #fff;
    }
    .btn-primary {
        background: ##003D4C;
        border: none;
        padding: 0.8rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    .btn-primary:hover {
        background: #5f61e6;
        transform: translateY(-2px);
    }
    .btn-primary:active {
        transform: translateY(0);
    }
    .passenger-card {
        background: #fff;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        margin-bottom: 1.5rem;
        padding: 1.5rem;
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
    }
    .passenger-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.08);
    }
    .passenger-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #edf2f7;
    }
    .passenger-title {
        color: #2c3e50;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
    }
    .passenger-title i {
        margin-left: 0.5rem;
        color: #696cff;
    }
    .btn-remove-passenger {
        background: #fee2e2;
        color: #dc2626;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }
    .btn-remove-passenger:hover {
        background: #fecaca;
        transform: translateY(-1px);
    }
    .btn-add-passenger {
        background: #696cff;
        color: white;
        border: none;
        padding: 0.8rem 1.5rem;
        border-radius: 10px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        margin-bottom: 1.5rem;
    }
    .btn-add-passenger:hover {
        background: #5f61e6;
        transform: translateY(-1px);
    }
    .passenger-form-group {
        margin-bottom: 1.5rem;
    }
    .price-display {
        font-size: 2.5rem;
        font-weight: 700;
        color: ##003D4C;
        text-shadow: 0 2px 4px rgba(105, 108, 255, 0.1);
    }
    .info-text {
        color: #64748b;
        font-size: 1rem;
        line-height: 1.7;
    }
    .form-check-input:checked {
        background-color: ##003D4C;
        border-color: ##003D4C;
    }
    .age-group-section {
        background: #f8fafc;
        padding: 1.5rem;
        border-radius: 12px;
        margin-top: 1rem;
    }
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255,255,255,0.8);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }
    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid ##003D4C;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    .error-feedback {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
    .success-feedback {
        color: #059669;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
    .customer-info-section {
        background: #fff;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.05);
        margin-bottom: 2rem;
        padding: 2rem;
    }
    .document-upload-area {
        border: 2px dashed #e2e8f0;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        background: #f8fafc;
        cursor: pointer;
    }
    .document-upload-area:hover {
        border-color: #696cff;
        background: #fff;
    }
    .document-upload-area i {
        font-size: 2rem;
        color: #696cff;
        margin-bottom: 1rem;
    }
    .upload-text {
        color: #64748b;
        margin-bottom: 0.5rem;
    }
    .submit-section {
        background: #fff;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.05);
        margin-top: 2rem;
        margin-bottom: 2rem;
        padding: 2rem;
        text-align: center;
    }
    .btn-submit {
        background: #003D4C;
        color: white;
        border: none;
        padding: 1rem 3rem;
        border-radius: 10px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        min-width: 200px;
    }
    .btn-submit:hover {
        background: #00526A;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .btn-submit:disabled {
        background: #cbd5e1;
        cursor: not-allowed;
        transform: none;
    }
    .passenger-documents {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e2e8f0;
    }
</style>
@endsection

@section('content')
<div class="container-xxl flex-grow-1 container-p-y">
    <div class="row">
        <div class="col-12">
            <div class="card form-section">
                <div class="card-body">
                    <h4 class="section-title">حاسبة قسط تأمين السفر</h4>
                    <p class="info-text">
                        نقدم لك أداة سهلة وذكية لحساب قسط تأمين السفر الخاص بك. أدخل تفاصيل رحلتك وعدد المسافرين للحصول على تقدير فوري للتكلفة.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <form id="travel-calc-form" action="{{ route('insurance-orders.store') }}" method="POST" enctype="multipart/form-data">
        @csrf
        <input type="hidden" name="type_id" value="3">

        <div class="row">
            <div class="col-12">
                <div class="card form-section">
                    <div class="card-body">
                        <h5 class="section-title">بيانات الرحلة</h5>
                        
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label class="form-label">المنطقة</label>
                                <select class="form-select select2" name="details[p_travel_to]" required>
                                    <option value="">اختر المنطقة</option>
                                    @foreach($locations as $location)
                                    <option value="{{$location['travel_to_code']}}">{{$location['travel_to_sname_a']}}</option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-4">
                                <label class="form-label">الدولة</label>
                                <select class="form-select select2" name="details[country]" required>
                                    <option value="">اختر الدولة</option>
                                    @foreach($travelcountries as $country)
                                    <option value="{{$country['country_code']}}">{{$country['country_aname']}}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label class="form-label">تاريخ المغادرة</label>
                                <input type="date" class="form-control" id="p_ins_start_dt" name="details[p_ins_start_dt]" required placeholder="اختر تاريخ المغادرة">
                            </div>
                            
                            <div class="col-md-6 mb-4">
                                <label class="form-label">تاريخ العودة</label>
                                <input type="date" class="form-control" id="p_ins_end_dt" name="details[p_ins_end_dt]" required placeholder="اختر تاريخ العودة">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <label class="form-label">عدد المسافرين الكلي</label>
                                <input type="number" class="form-control" id="number_of_travelers" name="details[number_of_travelers]" min="1" required>
                            </div>
                            
                            <div class="col-md-6 mb-4">
                                <label class="form-label d-block">هل أنت من ضمن المسافرين؟</label>
                                <div class="d-flex gap-4 mt-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="details[isUserTraveler]" id="userIsTravelerYes" value="yes">
                                        <label class="form-check-label" for="userIsTravelerYes">نعم</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="details[isUserTraveler]" id="userIsTravelerNo" value="no" checked>
                                        <label class="form-check-label" for="userIsTravelerNo">لا</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="age-group-section">
                            <h6 class="mb-3">توزيع الأعمار</h6>
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">أقل من 70 عام</label>
                                    <input type="number" class="form-control age-input" id="p_age_1" name="details[p_age_1]" min="0" value="0">
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">70 - 75 عام</label>
                                    <input type="number" class="form-control age-input" id="p_age_2" name="details[p_age_2]" min="0" value="0">
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">75 - 90 عام</label>
                                    <input type="number" class="form-control age-input" id="p_age_3" name="details[p_age_3]" min="0" value="0">
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">أعلى من 90 عام</label>
                                    <input type="number" class="form-control age-input" id="p_age_4" name="details[p_age_4]" min="0" value="0">
                                </div>
                            </div>
                        </div>

                        <div class="text-end mt-4">
                            <button type="button" class="btn btn-primary" id="calc-btn">
                                <i class="bx bx-calculator me-2"></i>
                                احسب القسط
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- نتائج حساب الاقساط --}}
        <div class="row">
            <div class="col-12">
                <div class="card form-section">
                    <div class="card-body text-center">
                        <h5 class="section-title">نتائج حساب الاقساط</h5>
                        <div class="price-display mb-3">
                            <span id="total_cost_display">0</span> شيقل
                        </div>
                        <input type="hidden" id="total_cost" name="total_cost" value="">
                    </div>
                </div>
            </div>
        </div>

        {{-- بيانات العميل --}}
        <div class="row">
            <div class="col-12">
                <div class="card form-section">
                    <div class="card-body">
                        <h5 class="section-title">
                            <i class="bx bx-user-circle me-2"></i>
                            بيانات العميل
                        </h5>
                        <div class="customer-info-section">
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label class="form-label">اسم المؤمن له بالعربي</label>
                                    <input type="text" class="form-control" name="details[insured_full_name]" value="{{ auth()?->user()?->fullname }}" required>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <label class="form-label">اسم المؤمن له بالإنجليزي</label>
                                    <input type="text" class="form-control" name="details[insured_en_full_name]" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label class="form-label">رقم الهوية</label>
                                    <input type="text" class="form-control" name="details[insured_identity]" value="{{ auth()?->user()?->identity }}" required>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <label class="form-label">تاريخ الميلاد</label>
                                    <input type="date" class="form-control flatpickr-input" name="details[insured_date_of_birth]" value="{{ auth()?->user()?->date_of_birth }}" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bx bx-envelope"></i>
                                        </span>
                                        <input type="email" class="form-control" name="details[insured_email]" value="{{ auth()?->user()?->email }}" required>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <label class="form-label">رقم الهاتف</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bx bx-phone"></i>
                                        </span>
                                        <input type="tel" class="form-control" name="details[insured_phone]" value="{{ auth()?->user()?->mobile }}" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-4">
                                    <label class="form-label">العنوان</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bx bx-map"></i>
                                        </span>
                                        <input type="text" class="form-control" name="details[insured_address]" value="{{ auth()?->user()?->address }}" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <label class="form-label">المستندات المطلوبة</label>
                                    <div class="document-upload-area" onclick="document.getElementById('document-upload').click()">
                                        <i class="bx bx-upload"></i>
                                        <p class="upload-text">اسحب وأفلت الملفات هنا أو انقر للتحميل</p>
                                        <small class="text-muted">يمكنك رفع عدة صور في نفس الوقت</small>
                                        <input type="file" id="document-upload" name="images[]" class="form-control d-none" accept="image/*" multiple>
                                    </div>
                                    <div id="preview-container" class="mt-3 row g-2"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- بيانات المسافرين --}}
        <div class="row">
            <div class="col-12">
                <div class="card form-section">
                    <div class="card-body">
                        <h5 class="section-title">بيانات المسافرين</h5>
                        <button type="button" class="btn-add-passenger" onclick="addPassenger()">
                            <i class="bx bx-user-plus"></i>
                            إضافة مسافر
                        </button>
                        <div id="passengersContainer"></div>
                    </div>
                </div>
            </div>
        </div>

        {{-- زر الإرسال --}}
        <div class="row">
            <div class="col-12">
                <div class="submit-section">
                    <button type="submit" class="btn btn-submit" id="submit-btn" >
                        <i class="bx bx-check-circle me-2"></i>
                        إرسال الطلب
                    </button>
                    <p class="text-muted mt-3">
                        بالضغط على زر الإرسال، أنت توافق على الشروط والأحكام
                    </p>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="loading-overlay">
    <div class="loading-spinner"></div>
</div>

@endsection

@section('page-script')
<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'classic',
        dir: 'rtl'
    });

    // Configure Flatpickr defaults
    flatpickr.localize(flatpickr.l10ns.ar);

    // Initialize date pickers for travel dates
    const startDatePicker = flatpickr("#p_ins_start_dt", {
        dateFormat: "Y-m-d",
        minDate: "today",
        allowInput: true,
        locale: "ar"
    });

    const endDatePicker = flatpickr("#p_ins_end_dt", {
        dateFormat: "Y-m-d",
        minDate: "today",
        allowInput: true,
        locale: "ar"
    });

    // Add event listeners for date range functionality
    if (startDatePicker && endDatePicker) {
        startDatePicker.config.onChange.push(function(selectedDates) {
            if (selectedDates[0]) {
                endDatePicker.set('minDate', selectedDates[0]);
            }
        });

        endDatePicker.config.onChange.push(function(selectedDates) {
            if (selectedDates[0]) {
                startDatePicker.set('maxDate', selectedDates[0]);
            }
        });
    }

    // Initialize birth date picker
    flatpickr("input[name='details[insured_date_of_birth]']", {
        dateFormat: "Y-m-d",
        maxDate: "today",
        allowInput: true,
        locale: "ar"
    });

    // Form Validation
    const form = document.getElementById('travel-calc-form');
    const calcBtn = document.getElementById('calc-btn');
    const submitBtn = document.getElementById('submit-btn');
    const loadingOverlay = document.querySelector('.loading-overlay');
    const ageInputs = document.querySelectorAll('.age-input');
    const totalTravelersInput = document.getElementById('number_of_travelers');

    // function validateForm() {
    //     let isValid = true;
    //     const requiredFields = form.querySelectorAll('[required]');
        
    //     requiredFields.forEach(field => {
    //         if (!field.value) {
    //             isValid = false;
    //             field.classList.add('is-invalid');
    //         } else {
    //             field.classList.remove('is-invalid');
    //         }
    //     });

    //     // Validate total travelers matches age distribution
    //     let totalAges = 0;
    //     ageInputs.forEach(input => {
    //         totalAges += parseInt(input.value || 0);
    //     });

    //     const totalTravelers = parseInt(totalTravelersInput.value || 0);
    //     if (totalAges !== totalTravelers) {
    //         isValid = false;
    //         totalTravelersInput.classList.add('is-invalid');
    //         if (!document.getElementById('travelers-error')) {
    //             const errorDiv = document.createElement('div');
    //             errorDiv.id = 'travelers-error';
    //             errorDiv.className = 'error-feedback';
    //             errorDiv.textContent = 'مجموع توزيع الأعمار يجب أن يساوي العدد الكلي للمسافرين';
    //             totalTravelersInput.parentNode.appendChild(errorDiv);
    //         }
    //     } else {
    //         totalTravelersInput.classList.remove('is-invalid');
    //         const errorDiv = document.getElementById('travelers-error');
    //         if (errorDiv) errorDiv.remove();
    //     }

    //     return isValid;
    // }
    // Update age distribution validation on input changes
    // totalTravelersInput.addEventListener('input', validateForm);
    // ageInputs.forEach(input => {
    //     input.addEventListener('input', validateForm);
    // });
});

let passengerCount = 0;

function addPassenger() {
    const index = passengerCount;
    const passengerHTML = `
        <div class="passenger-card" id="passenger-${index}">
            <div class="passenger-header">
                <h6 class="passenger-title">
                    <i class="bx bx-user"></i>
                    بيانات المسافر ${index + 1}
                </h6>
                <button type="button" class="btn-remove-passenger" onclick="removePassenger(${index})">
                    <i class="bx bx-trash"></i>
                    حذف
                </button>
            </div>
            <div class="row">
                <div class="col-md-6 mb-4">
                    <label class="form-label">رقم الجواز</label>
                    <input type="text" class="form-control" name="passengers[${index}][pass_passport_no]" required>
                </div>
                <div class="col-md-6 mb-4">
                    <label class="form-label">الاسم بالعربي</label>
                    <input type="text" class="form-control" name="passengers[${index}][pass_aname]" required>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 mb-4">
                    <label class="form-label">الاسم بالإنجليزي</label>
                    <input type="text" class="form-control" name="passengers[${index}][pass_lname]" required>
                </div>
                <div class="col-md-6 mb-4">
                    <label class="form-label">تاريخ انتهاء الجواز</label>
                    <input type="text" class="form-control datepicker passport-expiry" name="passengers[${index}][passport_edate]" required placeholder="اختر التاريخ">
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 mb-4">
                    <label class="form-label">تاريخ الميلاد</label>
                    <input type="text" class="form-control datepicker birth-date" name="passengers[${index}][pass_dob]" required placeholder="اختر التاريخ">
                </div>
                <div class="col-md-6 mb-4">
                    <label class="form-label">رقم الهاتف</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bx bx-phone"></i>
                        </span>
                        <input type="tel" class="form-control" name="passengers[${index}][pass_mob]" required>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 mb-4">
                    <label class="form-label">العنوان</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bx bx-map"></i>
                        </span>
                        <input type="text" class="form-control" name="passengers[${index}][pass_address]" required>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <label class="form-label">نوع جواز السفر</label>
                    <select class="form-select" name="passengers[${index}][pass_passport_type]" required>
                        <option value="">اختر النوع</option>
                        <option value="عادي">عادي</option>
                        <option value="دبلوماسي">دبلوماسي</option>
                        <option value="خاص">خاص</option>
                    </select>
                </div>
            </div>
            <div class="passenger-documents">
                <label class="form-label">المستندات المطلوبة</label>
                <div class="document-upload-area" onclick="document.getElementById('passenger-docs-${index}').click()">
                    <i class="bx bx-upload"></i>
                    <p class="upload-text">اسحب وأفلت الملفات هنا أو انقر للتحميل</p>
                    <small class="text-muted">يمكنك رفع صور جواز السفر والمستندات المطلوبة</small>
                    <input type="file" id="passenger-docs-${index}" name="passengers[${index}][documents][]" class="form-control d-none" accept="image/*" multiple>
                </div>
                <div id="preview-container-${index}" class="mt-3 row g-2"></div>
            </div>
        </div>
    `;

    document.getElementById("passengersContainer").insertAdjacentHTML('beforeend', passengerHTML);
    
    // Initialize datepickers for the new passenger
    const newPassengerCard = document.getElementById(`passenger-${index}`);
    
    // Initialize passport expiry datepicker with specific configuration
    flatpickr(newPassengerCard.querySelector('.passport-expiry'), {
        dateFormat: "Y-m-d",
        minDate: "today",
        locale: "ar",
        disableMobile: "true"
    });

  
    
    // Initialize birth date datepicker with specific configuration
    flatpickr(newPassengerCard.querySelector('.birth-date'), {
        dateFormat: "Y-m-d",
        maxDate: "today",
        locale: "ar",
        disableMobile: "true"
    });

    // Initialize document upload preview
    const docInput = document.getElementById(`passenger-docs-${index}`);
    docInput.addEventListener('change', function(e) {
        const container = document.getElementById(`preview-container-${index}`);
        container.innerHTML = '';
        
        Array.from(e.target.files).forEach((file, fileIndex) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.createElement('div');
                preview.className = 'col-md-3 col-sm-4 col-6';
                preview.innerHTML = `
                    <div class="position-relative">
                        <img src="${e.target.result}" class="img-fluid rounded" style="height: 150px; width: 100%; object-fit: cover;">
                        <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1" onclick="removePassengerDoc(this, ${index}, ${fileIndex})">
                            <i class="bx bx-x"></i>
                        </button>
                    </div>
                `;
                container.appendChild(preview);
            }
            reader.readAsDataURL(file);
        });
    });
    
    passengerCount++;
}

function removePassenger(index) {
    const el = document.getElementById(`passenger-${index}`);
    if (el) {
        el.style.transform = 'translateY(10px)';
        el.style.opacity = '0';
        setTimeout(() => {
            el.remove();
            passengerCount--;
        }, 300);
    }
}

function removePassengerDoc(button, passengerIndex, fileIndex) {
    const input = document.getElementById(`passenger-docs-${passengerIndex}`);
    
    // Remove the preview
    button.closest('.col-md-3').remove();
    
    // Create a new FileList without the removed file
    const dt = new DataTransfer();
    Array.from(input.files).forEach((file, i) => {
        if(i !== fileIndex) dt.items.add(file);
    });
    input.files = dt.files;
}

// Form submission handling
document.getElementById('travel-calc-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
   

    const totalTravelers = parseInt(document.getElementById('number_of_travelers').value || 0);
    const currentPassengers = document.querySelectorAll('.passenger-card').length;
    
    if (currentPassengers !== totalTravelers) {
        alert('عدد المسافرين المضافين يجب أن يساوي العدد الكلي للمسافرين');
        return;
    }

    // If all validations pass, submit the form
    this.submit();
});

// Document upload preview
document.getElementById('document-upload').addEventListener('change', function(e) {
    const container = document.getElementById('preview-container');
    container.innerHTML = '';
    
    Array.from(e.target.files).forEach((file, index) => {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.createElement('div');
            preview.className = 'col-md-3 col-sm-4 col-6';
            preview.innerHTML = `
                <div class="position-relative">
                    <img src="${e.target.result}" class="img-fluid rounded" style="height: 150px; width: 100%; object-fit: cover;">
                    <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1" onclick="removePreview(this, ${index})">
                        <i class="bx bx-x"></i>
                    </button>
                </div>
            `;
            container.appendChild(preview);
        }
        reader.readAsDataURL(file);
    });
});

function removePreview(button, index) {
    const input = document.getElementById('document-upload');
    const container = document.getElementById('preview-container');
    
    // Remove the preview
    button.closest('.col-md-3').remove();
    
    // Create a new FileList without the removed file
    const dt = new DataTransfer();
    Array.from(input.files).forEach((file, i) => {
        if(i !== index) dt.items.add(file);
    });
    input.files = dt.files;
}
$(document).ready(function() {
    $('#calc-btn').click(function(event) {
        event.preventDefault();

        console.log('test'); // <-- fixed here

        var formData = {
            p_travel_to: $('#p_travel_to').val(),
            p_ins_start_dt: $('#p_ins_start_dt').val(),
            p_ins_end_dt: $('#p_ins_end_dt').val(),
            p_covid: 0,
            p_age_1: $('#p_age_1').val(),
            p_age_2: $('#p_age_2').val(),
            p_age_3: $('#p_age_3').val(),
            p_age_4: $('#p_age_4').val(),
            p_age_5: 0,
            access_token: $('meta[name="csrf-token"]').attr('content')
        };

        $.ajax({
            url: 'http://**********:8000/travel-price',
            type: 'GET',
            data: formData,
            success: function(response) {
                console.log(response);
            },
            error: function(xhr, status, error) {
                console.log(xhr
                    .responseText
                ); // this is better than `response` which is undefined here
                $('#responseMessage').html('<p>Error: ' + error + '</p>');
            }
        });
    });
});
</script>
@endsection