@extends('layouts.wordpressLayout')

@section('title', 'حاسبة قسط تأمين السفر')

@section('content')
<div class="row" style="">
    <h5 class="mb-0">حاسبة قسط تأمين السفر</h5>
    <p class="text-muted float-end">
        توفر شركة التكافل أداة ذكية وسهلة الاستخدام لحساب قسط تأمين السفر بدقة وسرعة. تتيح لك الحاسبة إدخال معلومات
        الرحلة الأساسية مثل الوجهة، مدة السفر، عدد المسافرين، ونوع التغطية المطلوبة، لتحصل فورًا على تقدير تقريبي لقيمة
        القسط التأميني.

        تهدف هذه الخدمة إلى مساعدة المسافرين على التخطيط المالي بشكل أفضل ومعرفة التكاليف المتوقعة قبل شراء بوليصة تأمين
        السفر، بكل شفافية وسلاسة.
    </p>

    <form id=" car-calc-form" action="{{ route('insurance-orders.store') }}" method="POST">
        @csrf

        {{-- بيانات الرحلة --}}
        <div class="col-xxl">
            <div class="card mb-6">
                <div class="card-body">
                    <div class="col-xxl">
                        <div class="card mb-6">
                            <div class="card-body">
                                <div class="row mb-3">
                                    <label class="col-sm-3 col-form-label">المنطقة</label>
                                    <div class="col-sm-9">
                                        <select class="form-select" name="details[p_travel_to]">
                                            @foreach($locations as $location)
                                            <option value="{{$location['travel_to_code']}}"
                                                {{$order?->TravelInsuranceDetails?->p_travel_to == $location['travel_to_code'] ? 'selected' : ''}}>
                                                {{$location['travel_to_sname_a']}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <label class="col-sm-3 col-form-label">الجهة المسافر اليها</label>
                                    <div class="col-sm-9">
                                        <select class="form-select" name="details[p_travel_to]">
                                            @foreach($travelcountries as $item)
                                            <option value="{{$item['country_code']}}"
                                                {{$order?->TravelInsuranceDetails?->p_travel_to == $item['country_code'] ? 'selected' : ''}}>
                                                {{$item['country_aname']}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                @php
                                $fields = [
                                ['label' => 'تاريخ المغادرة', 'id' => 'p_ins_start_dt', 'type' => 'date'],
                                ['label' => 'تاريخ العودة', 'id' => 'p_ins_end_dt', 'type' => 'date'],
                                ['label' => 'عدد المسافرين الكلي', 'id' => 'number_of_travelers', 'type' => 'number'],
                                ['label' => 'عدد المسافرين الذين أعمارهم أقل من 70 عام', 'id' => 'p_age_1', 'type' =>
                                'number'],
                                ['label' => 'عدد المسافرين بين 70 - 75 عام', 'id' => 'p_age_2', 'type' => 'number'],
                                ['label' => 'عدد المسافرين بين 75 - 90 عام', 'id' => 'p_age_3', 'type' => 'number'],
                                ['label' => 'عدد المسافرين أعلى من 90 عام', 'id' => 'p_age_4', 'type' =>
                                'number'],
                                ];
                                $detail = $order->TravelInsuranceDetails->first(); // or [0] if it's an array

                                @endphp

                                @foreach($fields as $field)
                                <div class="row mb-3">
                                    <label class="col-sm-3 col-form-label"
                                        for="{{ $field['id'] }}">{{ $field['label'] }}</label>
                                    <div class="col-sm-9">
                                        <input type="{{ $field['type'] }}" id="{{ $field['id'] }}"
                                            name="details[{{ $field['id'] }}]" class="form-control"
                                            value="{{$detail[$field['id']] ?? ''}}" />
                                    </div>
                                </div>
                                @endforeach

                                {{-- هل المستخدم من ضمن المسافرين؟ --}}
                                <div class="row mb-3">
                                    <label class="col-sm-3 col-form-label">هل أنت من ضمن المسافرين؟</label>
                                    <div class="col-sm-9">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="details[isUserTraveler]"
                                                id="userIsTravelerYes" value="yes">
                                            <label class="form-check-label" for="userIsTravelerYes">نعم</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="details[isUserTraveler]"
                                                id="userIsTravelerNo" value="no" checked>
                                            <label class="form-check-label" for="userIsTravelerNo">لا</label>
                                        </div>
                                    </div>
                                </div>

                                <div id="responseMessage" class="mt-3 text-success"></div>
                            </div>
                        </div>
                    </div>

                    {{-- نتائج حساب الاقساط --}}
                    <div class="row mb-4">
                        <div class="card shadow-sm">
                            <div class="card-body text-center">
                                <h5 class="card-title mb-4">نتائج حساب الاقساط</h5>
                                <div class="mt-4">
                                    <label class="form-label fw-bold">تكلفة التأمين</label>
                                    <input type="text" readonly class="form-control text-center border-primary fw-bold"
                                        id="total_cost" name="total_cost" value="">
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- بيانات العميل --}}
                    <div class="col-xxl">
                        <div class="card shadow-sm">
                            <div class="card-body text-center">
                                <h5 class="card-title mb-4">بيانات العميل</h5>
                                @php
                                $userFields = [
                                ['label' => 'اسم المؤمن له', 'id' => 'customer_name', 'name' => 'insured_full_name',
                                'value' => auth()?->user()?->fullname],
                                ['label' => 'رقم الهوية', 'id' => 'customer_id', 'name' => 'insured_identity', 'value'
                                => auth()?->user()?->identity],
                                ['label' => 'تاريخ الميلاد', 'id' => 'customer_dob', 'name' => 'insured_date_of_birth',
                                'value' => auth()?->user()?->date_of_birth],
                                ['label' => 'البريد الإلكتروني', 'id' => 'customer_email', 'name' => 'insured_email',
                                'value' => auth()?->user()?->email],
                                ['label' => 'رقم الهاتف', 'id' => 'customer_phone', 'name' => 'insured_phone', 'value'
                                => auth()?->user()?->mobile],
                                ];
                                @endphp

                                @foreach($userFields as $field)
                                <div class="row mb-3">
                                    <label class="col-sm-3 col-form-label"
                                        for="{{ $field['id'] }}">{{ $field['label'] }}</label>
                                    <div class="col-sm-9">
                                        <input type="text" name="details[{{ $field['name'] }}]" id="{{ $field['id'] }}"
                                            class="form-control" value="{{ $field['value'] }}">
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    {{-- Hidden Inputs --}}
                    <input type="hidden" name="type_id" value="3">
                    <input type="hidden" name="details[p_prem]" value="">
                    <input type="hidden" name="details[p_fees]" value="">

                    <div id="passengersContainer"></div>
                    <div class="mb-3" class="mb-3 d-none">
                        <button disabled type="button" class="btn btn-primary" onclick="addPassenger()">➕ إضافة
                            مسافر</button>
                    </div>

                    <div disabled class="d-grid gap-2 col-lg-4 mx-auto">
                        <button disabled type="submit" class="btn btn-primary btn-lg">إرسال الطلب</button>
                    </div>
    </form>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('input, button, select, textarea').forEach(el => el.disabled = true);
})
// Pass PHP passenger data to JS
let passengerData = @json($order -> TravelInsuranceDetails -> passengersOreder);
let passengerCount = 0;
</script>


<script>
// Generate input with default value
function generateInput(label, field, index, type, value = '') {
    return `
      <div class="row mb-3">
        <label class="col-sm-3 col-form-label" for="passenger_${index}_${field}">${label}</label>
        <div class="col-sm-9">
          <input disabled type="${type}" id="passenger_${index}_${field}" name="passengers[${index}][${field}]" class="form-control" value="${value ?? ''}" />
        </div>
      </div>
    `;
}

// Add one passenger block
function addPassengerBlock(passenger = {}, index = passengerCount) {
    const passengerHTML = `
      <div class="col-xxl" id="passenger-${index}">
        <div class="card mb-4">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
              <h5 class="card-title mb-0">بيانات المسافر ${index + 1}</h5>
            </div>

            ${generateInput('رقم الجواز', 'pass_passport_no', index, 'text', passenger.pass_passport_no)}
            ${generateInput('الاسم بالعربي', 'pass_aname', index, 'text', passenger.pass_aname)}
            ${generateInput('الاسم بالإنجليزي', 'pass_lname', index, 'text', passenger.pass_lname)}
            ${generateInput('تاريخ انتهاء الجواز', 'passport_edate', index, 'date', passenger.passport_edate)}
            ${generateInput('تاريخ الميلاد', 'pass_dob', index, 'date', passenger.pass_dob)}
            ${generateInput('رقم الجوال', 'pass_mob', index, 'text', passenger.pass_mob)}
            ${generateInput('العنوان', 'pass_address', index, 'text', passenger.pass_address)}
            ${generateInput('حسبة القسط', 'pass_prem', index, 'number', passenger.pass_prem)}

            <div class="row mb-3">
              <label class="col-sm-3 col-form-label">نوع جواز السفر</label>
              <div class="col-sm-9">
                <select disabled class="form-select" name="passengers[${index}][pass_passport_type]">
                  <option value="">اختر النوع</option>
                  <option value="عادي" ${passenger.pass_passport_type === 'عادي' ? 'selected' : ''}>عادي</option>
                  <option value="دبلوماسي" ${passenger.pass_passport_type === 'دبلوماسي' ? 'selected' : ''}>دبلوماسي</option>
                  <option value="خاص" ${passenger.pass_passport_type === 'خاص' ? 'selected' : ''}>خاص</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    document.getElementById("passengersContainer").insertAdjacentHTML('beforeend', passengerHTML);
    passengerCount++;
}

// On page load, render existing passengers
window.addEventListener('DOMContentLoaded', () => {

        if (Array.isArray(passengerData)) {
            passengerData.forEach((passenger, i) => {
                addPassengerBlock(passenger, i);
            });
        }
    }

);
</script>
@endsection