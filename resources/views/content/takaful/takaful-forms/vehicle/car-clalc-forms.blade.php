@extends('layouts/wordpressLayout')

@section('title', ' حاسبة قسط تأمين المركبات')

{{-- Required CSS --}}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/airbnb.css">
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

@section('page-style')
<style>
    :root {
        --primary-color: #003D4C;
        --secondary-color: #0094B3;
        --border-color: #e2e8f0;
        --bg-hover: rgba(0, 148, 179, 0.05);
    }

    .form-section {
        background: #fff;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.05);
        padding: 2rem;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }

    .form-section:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .section-title {
        color: var(--primary-color);
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--border-color);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-title i {
        color: var(--secondary-color);
        font-size: 1.75rem;
    }

    .form-control, .form-select {
        border-radius: 10px;
        border: 1.5px solid var(--border-color);
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
        background-color: #f8fafc;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--secondary-color);
        box-shadow: 0 0 0 3px rgba(0, 148, 179, 0.1);
        background-color: #fff;
    }

    .input-group-text {
        background: #f8fafc;
        border: 1.5px solid var(--border-color);
        border-radius: 10px;
        color: var(--secondary-color);
    }

    .form-label {
        font-weight: 500;
        color: #4a5568;
        margin-bottom: 0.5rem;
    }

    .btn-primary {
        background: var(--primary-color);
        border: none;
        padding: 0.8rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
        color: white;
    }

    .btn-primary:hover {
        background: var(--secondary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .form-check {
        padding: 0.75rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        margin-bottom: 0.5rem;
        cursor: pointer;
    }

    .form-check:hover {
        background: var(--bg-hover);
    }

    .form-check-input:checked {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .price-display {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--primary-color);
        text-align: center;
        margin: 1.5rem 0;
        text-shadow: 0 2px 4px rgba(0, 61, 76, 0.1);
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255,255,255,0.9);
        backdrop-filter: blur(5px);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .submit-section {
        text-align: center;
        margin-top: 3rem;
        padding: 2rem;
        background: #fff;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.05);
    }

    .btn-submit {
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 1rem 3rem;
        border-radius: 10px;
        font-weight: 600;
        font-size: 1.1rem;
        min-width: 200px;
        transition: all 0.3s ease;
    }

    .btn-submit:hover {
        background: var(--secondary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .btn-submit:disabled {
        background: #cbd5e1;
        cursor: not-allowed;
        transform: none;
    }

    .select2-container--classic .select2-selection--single {
        height: calc(2.5rem + 2px) !important;
        border: 1.5px solid var(--border-color) !important;
        border-radius: 10px !important;
        background: #f8fafc !important;
    }

    .select2-container--classic .select2-selection--single:focus {
        border-color: var(--secondary-color) !important;
        box-shadow: 0 0 0 3px rgba(0, 148, 179, 0.1) !important;
    }

    .select2-container--classic .select2-selection--single .select2-selection__rendered {
        line-height: calc(2.5rem + 2px) !important;
        color: #4a5568 !important;
    }

    .select2-container--classic .select2-selection--single .select2-selection__arrow {
        height: calc(2.5rem + 2px) !important;
        background: var(--primary-color) !important;
        border: none !important;
        border-radius: 0 10px 10px 0 !important;
        width: 30px !important;
    }

    .select2-container--classic .select2-selection--single .select2-selection__arrow b {
        border-color: #fff transparent transparent transparent !important;
    }

    .document-upload {
        border: 2px dashed var(--border-color);
        border-radius: 10px;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        background: #f8fafc;
        cursor: pointer;
    }

    .document-upload:hover {
        border-color: var(--secondary-color);
        background: #fff;
    }

    .preview-image {
        max-width: 100px;
        border-radius: 8px;
        margin-top: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    /* RTL Specific Styles */
    .rtl {
        direction: rtl;
        text-align: right;
    }

    .rtl .input-group > .form-control {
        border-radius: 10px 0 0 10px;
    }

    .rtl .input-group > .input-group-text {
        border-radius: 0 10px 10px 0;
    }

    .rtl .me-2 {
        margin-left: 0.5rem !important;
        margin-right: 0 !important;
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .form-section {
            padding: 1.5rem;
        }

        .section-title {
            font-size: 1.25rem;
        }

        .price-display {
            font-size: 2rem;
        }

        .btn-submit {
            width: 100%;
        }
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Basic Layout & Basic with Icons -->
            <div class="container-xxl flex-grow-1 container-p-y">
                <div class="row">
                    <div class="col-12">
                        <div class="card form-section">
                            <div class="card-body">
                                <h4 class="section-title">حاسبة قسط تأمين المركبات</h4>
                                <p class="info-text">
                                    توفر شركة التكافل أداة ذكية وسهلة الاستخدام لحساب قسط تأمين مركبتك بدقة وسرعة. تتيح لك الحاسبة إدخال معلومات المركبة الأساسية مثل نوع المركبة، سنة الصنع، سعة المحرك، ونوع التأمين المطلوب (شامل أو ضد الغير)، لتحصل فورًا على تقدير تقريبي لقيمة القسط التأميني.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <form id="car-calc-form" action="{{ route('insurance-orders.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <div class="row">
                        <div class="col-12">
                            <div class="card form-section">
                                <div class="card-body">
                                    <h5 class="section-title">بيانات المركبة</h5>
                                    
                                    <div class="row mb-6">
                                        <label class="col-sm-3 col-form-label" for="location">المنطقة / التعرفة</label>
                                        <div class="col-sm-9">
                                            <select class="form-select" name="details[policy_location]" id="location">
                                                <option selected value="pal">فلسطينية</option>
                                                <option value="quds">القدس</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Insurance Type Section -->
                                    <div class="row mb-6" id="insurance-unified">
                                        <label class="col-sm-3 col-form-label">نوع التأمين</label>
                                        <div class="col-sm-9">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="insurance_type" value="102" id="insurance0">
                                                <label class="form-check-label" for="insurance0">
                                                    تأمين الزامي و طرف ثالث (الموحد)
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="insurance_type" value="شامل" id="insurance1">
                                                <label class="form-check-label" for="insurance1">
                                                    شامل
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mb-6 d-none" id="insurance-detailed">
                                        <label class="col-sm-3 col-form-label">نوع التأمين</label>
                                        <div class="col-sm-9">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="insurance_type"
                                                    value="تأمين الزامي" id="insurance2">
                                                <label class="form-check-label" for="insurance2">تأمين الزامي</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="insurance_type" value="شامل"
                                                    id="insurance3">
                                                <label class="form-check-label" for="insurance3">شامل</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="insurance_type" value="طرف ثالث"
                                                    id="insurance4">
                                                <label class="form-check-label" for="insurance4">طرف ثالث</label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Rest of the form fields -->
                                    <div class="row mb-6">
                                        <label class="col-sm-3 col-form-label" for="used-type">نوع الترخيص</label>
                                        <div class="col-sm-9">
                                            <select name="details[policy_use]" id="item_id" class="form-select"
                                                aria-label="Default select example">
                                                <option selected value="0">اختر</option>
                                                @foreach($items as $item)
                                                <option value="{{ $item['pol_use'] }}">{{ $item['pol_use_adesc'] }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row mb-6">
                                        <label class="col-sm-3 col-form-label" for="used-type-spc">الاستعمال الخاص</label>
                                        <div class="col-sm-9">
                                            <select class="form-select" name="details[policy_spical_use]" id="used-type-spc"
                                                aria-label="Default select example">
                                                <option selected value=""></option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row mb-6" id="engine-capacity">
                                        <label class="col-sm-3 col-form-label" for="engine-capacity">مواصفات المركبة <br> (سعة
                                            المحرك)</label>
                                        <div class="col-sm-9">
                                            <select class="form-select" name="details[policy_cc]" id="engine-capacity-val"
                                                aria-label="Default select example ">
                                                <option value="1200">1200</option>
                                                <option value="1400">1400</option>
                                                <option value="1600">1600</option>
                                                <option value="1800">1800</option>
                                                <option value="2000">2000</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row mb-6" id="weight-field">
                                        <label class="col-sm-3 col-form-label" for="waight"> الوزرن بالطن</label>
                                        <div class="col-sm-9">
                                            <input type="text" id="waight" name=" details[policy_load]" class="form-control phone-mask"
                                                aria-describedby="basic-default-phone" />
                                        </div>
                                    </div>
                                    <div class="row mb-6" id="passenger-number">
                                        <label class="col-sm-3 col-form-label" for="number-passenger"> عدد الركاب</label>
                                        <div class="col-sm-9">
                                            <input type="text" name="details[policy_seats]" id="number-passenger"
                                                class="form-control phone-mask" aria-describedby="basic-default-phone" />
                                        </div>
                                    </div>
                                    <div class="row mb-6">
                                        <label class="col-sm-3 col-form-label" for="insurance-type">نوع فترة التأمين</label>
                                        <div class="col-sm-9">
                                            <select class="form-select" id="insurance-type" aria-label="Default select example">
                                                <option selected disabled>اختر</option>
                                                <option value="1">سنة</option>
                                                <option value="2">شهر</option>
                                                <option value="3">يوم</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row mb-6">
                                        <label class="col-sm-3 col-form-label" for="insurance-period">فترة التأمين المطلوبة</label>
                                        <div class="col-sm-9">
                                            <input type="number" id="insurance-period" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="row mb-6">
                                        <label class="col-sm-3 col-form-label" for="basic-default-phone">
                                            مبلغ التأمين<br><small>(سعر المركبة - شيكل)</small>
                                        </label>
                                        <div class="col-sm-9">
                                            <input type="text" id="price-car" name=" details[policy_inurance_vechile_price]"
                                                class="form-control phone-mask" aria-describedby="basic-default-phone" />
                                        </div>
                                    </div>
                                    <div class="row mb-6">
                                        <label class="col-sm-3 col-form-label" for="basic-default-phone"> سنة الصنع</label>
                                        <div class="col-sm-9">
                                            <input type="text" id="date-prod" name=" details[policy_prod_year]"
                                                class="form-control phone-mask" aria-describedby="basic-default-phone" />
                                        </div>
                                    </div>

                                    <div class="text-end mt-4">
                                        <button type="button" class="btn btn-primary" id="calc-btn">
                                            <i class="bx bx-calculator me-2"></i>
                                            احسب القسط
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Results Section -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card form-section">
                                <div class="card-body text-center">
                                    <h5 class="section-title">نتائج حساب الاقساط</h5>
                                    <div class="mt-4">
                                        <label class="form-label">قسط التأمين الإلزامي و الطرف الثالث</label>
                                        <input type="text" readonly class="form-control text-center" id="insuranceAmount" value="">
                                    </div>
                                    <div class="mt-4">
                                        <label class="form-label">قسط التأمين (الشامل)</label>
                                        <input type="text" id='compValue' readonly class="form-control text-center" value="">
                                    </div>
                                    <div class="mt-4" id="fullcost" style="display: none;">
                                        <label class="form-label fw-bold">تكلفة التأمين</label>
                                        <div class="price-display" id='full' name="total_cost"></div>
                                    </div>
                                    <div class="row justify-content-end mt-4">
                                        <div class="col-sm-10">
                                            <button onclick="CompleteForms()" type="button" class="btn btn-submit">تكملة الطلب</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                       <input type="hidden" id="type_id" name="type_id" value="1">
                    <input type="hidden" id="policy_vechile_class_type" name="details[policy_vechile_class_type]" value="">
                    <input type="hidden" id="act_price" name="details[act_price]" value="">
                    <input type="hidden" id="tp_price" name="details[tp_price]" value="">
                    <input type="hidden" id="comp_price" name="details[comp_price]" value="">
                    <input type="hidden" id="total_cost" name="total_cost" value="">

                    <!-- Customer Info Section -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card form-section">
                                <div class="card-body">
                                    <h5 class="section-title">
                                        <i class="bx bx-user-circle me-2"></i>
                                        بيانات العميل
                                    </h5>
                                    <div class="customer-info-section">
                                        <div class="row mb-6">
                                            <label class="col-sm-3 col-form-label" for="name">اسم المؤمن له </label>
                                            <div class="col-sm-9">
                                                <input type="text" name="details[insured_full_name]" id="customer_name"
                                                    class="form-control phone-mask" aria-describedby="basic-default-phone"
                                                    value="{{auth()->user()->fullname}}" />
                                            </div>
                                        </div>
                                        <div class="row mb-6">
                                            <label class="col-sm-3 col-form-label" for="id">رقم هوية المؤمن له </label>
                                            <div class="col-sm-9">
                                                <input type="text" name="details[insured_identity]" id="customer_id"
                                                    class="form-control phone-mask" aria-describedby="basic-default-phone"
                                                    value="{{auth()->user()->identity}}" />
                                            </div>
                                        </div>
                                        <div class="row mb-6">
                                            <label class="col-sm-3 col-form-label" for="dob">تاريخ ميلاد المؤمن له </label>
                                            <div class="col-sm-9">
                                                <input type="text" name="details[insured_date_of_birth]" id="customer_dob"
                                                    class="form-control phone-mask" aria-describedby="basic-default-phone"
                                                    value="{{auth()->user()->date_of_birth}}" />
                                            </div>
                                        </div>
                                        <div class="row mb-6">
                                            <label class="col-sm-3 col-form-label" for="email">البريد الالكتروني المؤمن له</label>
                                            <div class="col-sm-9">
                                                <input type="text" name="details[insured_emai]" id="customer_email"
                                                    class="form-control phone-mask" aria-describedby="basic-default-phone"
                                                    value="{{auth()->user()->email}}" />
                                            </div>
                                        </div>
                                        <div class="row mb-6">
                                            <label class="col-sm-3 col-form-label" for="phone">رقم هاتف المؤمن له </label>
                                            <div class="col-sm-9">
                                                <input type="text" name="details[insured_phone]" id="customer_phone"
                                                    class="form-control phone-mask" aria-describedby="basic-default-phone"
                                                    value="{{auth()->user()->mobile}}" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                 

                    <!-- Complete Form Section -->
                    <div class="row">
                        <div class="col-12" id='complet-section' style="display: none;">
                            <div class="card form-section">
                                <div class="card-body">
                                    <h5 class="section-title">تفاصيل التأمين</h5>
                                    <div class="row mb-6">
                                        <label class="col-sm-3 col-form-label" for="start-insu"> بداية التأمين</label>
                                        <div class="col-sm-9">
                                            <input type="date" name="details[start_insurance_date]" id="startdate"
                                                class="form-control phone-mask" aria-describedby="basic-default-phone" />
                                        </div>
                                    </div>
                                    <div class="row mb-6">
                                        <label class="col-sm-3 col-form-label" for="end-insu"> نهاية التأمين</label>
                                        <div class="col-sm-9">
                                            <input disabled type="date" name="details[end_insurance_date]" id="expiration-date"
                                                class="form-control phone-mask" aria-describedby="basic-default-phone" />
                                        </div>
                                    </div>
                                    <div class="row mb-6">
                                        <label class="col-sm-3 col-form-label" for="calc">مجموع القسط</label>
                                        <div class="col-sm-9">
                                            <input disabled type="text" id="totalcost" name="details[total_cost]"
                                                class="form-control phone-mask" aria-describedby="basic-default-phone" />
                                        </div>
                                    </div>
                                    <div class="row mb-6">
                                        <label class="col-sm-3 col-form-label" for="chase-number"> رقم الشصي </label>
                                        <div class="col-sm-9">
                                            <input type="text" name="details[chasi_number]" id="chase" class="form-control phone-mask"
                                                aria-describedby="basic-default-phone" />
                                        </div>
                                    </div>
                                    <div class="row mb-5" id="authorized-drivers-question" dir="rtl" style="display:none;">
                                        <label class="col-sm-3 col-form-label text-end">هل يوجد سائق أقل من 24 سنة أو يحمل رخصة أقل من سنة؟ <span style="color:red">*</span></label> <br> </div>
                                        <div id="authorized-drivers-question" class="col-sm-9" >
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="details[young_or_new_driver]" id="driverYes" value="yes" required>
                                                <label class="form-check-label" for="driverYes">نعم</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="details[young_or_new_driver]" id="driverNo" value="no" required>
                                                <label class="form-check-label" for="driverNo">لا</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mb-6">
                                        <label class="col-sm-3 col-form-label" for="name"> اسماء المخولين في القيادة</label>
                                        <div class="col-sm-9">
                                            <input type="text" name="details[who_can_drive_name]" id="namecandrive"
                                                class="form-control phone-mask" aria-describedby="basic-default-phone" />
                                        </div>
                                    </div>
                                    <div class="row mb-6">
                                        <label class="col-sm-3 col-form-label" for="idcandrive"> ارقام هويات المخولين بالقيادة</label>
                                        <div class="col-sm-9">
                                            <input type="text" name="details[who_can_drive_identity]" id="idcandrive"
                                                class="form-control phone-mask" aria-describedby="basic-default-phone" />
                                        </div>
                                    </div>
                                    <div class="row mb-6">
                                        <label class="col-sm-3 col-form-label" for="car-numver"> رقم لوحة المركبة </label>
                                        <div class="col-sm-9">
                                            <input type="text" id="car-number" name="details[plate_number]" class="form-control phone-mask"
                                                aria-describedby="basic-default-phone" />
                                        </div>
                                    </div>
                                    <div class="row mb-6">
                                        <label class="col-sm-3 col-form-label" for="car-type"> نوع المركبة </label>
                                        <div class="col-sm-9">
                                            <select class="form-select" name="details[vechile_type]" id="car-type"
                                                aria-label="Default select example">
                                                <option value="selected">اختار</option>
                                                @foreach($vehcilType as $vehcil)
                                                <option value="{{$vehcil['mot_brand_code']}}">{{$vehcil['mot_brand_a']}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row mb-6">
                                        <label class="col-sm-3 col-form-label" for="car-model">موديل السيارة </label>
                                        <div class="col-sm-9">
                                            <select class="form-select" name="details[vechile_model]" id="car-model"
                                                aria-label="Default select example">
                                                <option selected value=""></option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row mb-6">
                                        <label class="col-sm-3 col-form-label" for="desiel-type"> نوع الوقود </label>
                                        <div class="col-sm-9">
                                            <select class="form-select" name="details[feul_type]" id="desiel-type"
                                                aria-label="Default select example">
                                                <option value="اختيار">اختيار</option>
                                                @foreach($enginesource as $engin)
                                                <option value="{{$engin['eng_source_code']}}"> {{$engin['eng_source_adesc']}} </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row mb-6">
                                        <label class="col-sm-3 col-form-label">صور المركبة (7 صور)</label>
                                        <div class="col-sm-9">
                                            @for ($i = 1; $i <= 7; $i++) <div class="mb-2">
                                                <input type="file" name="images[]" class="form-control" accept="image/*"
                                                    onchange="previewImage(event, {{ $i }})">
                                                <img id="preview-{{ $i }}" src="" alt="Preview"
                                                    style="display:none; max-width: 100px; margin-top: 10px;" />
                                        </div>
                                        @endfor
                                    </div>
                                   
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Section -->
                    <div class="row">
                        <div class="col-12">
                            <div class="submit-section">
                                <button type="submit" class="btn btn-submit">
                                    <i class="bx bx-check-circle me-2"></i>
                                    حفظ الطلب
                                </button>
                                <p class="text-muted mt-3">
                                    بالضغط على زر الإرسال، أنت توافق على الشروط والأحكام
                                </p>
                            </div>
                        </div>
                    </div>
                </form>

                <div class="loading-overlay">
                    <div class="loading-spinner"></div>
                </div>
            </div>
        </div>
    </div>
        </div>

</div>
@endsection
@section('scripts')
<script>
     document.getElementById('location').addEventListener('change', function () {
        // Get all checkboxes with name insurance_type
        let checkboxes = document.querySelectorAll('input[name="insurance_type"]');
        
        checkboxes.forEach(function (checkbox) {
            checkbox.checked = false;  // uncheck
        });
    });
function getInsuranceCode() {
    let selected = [];
    document.querySelectorAll('input[name="insurance_type"]:checked').forEach((el) => {
        selected.push(el.value);
    });
    selected.sort(); // مهم جداً لترتيب القيم
    const combinationMap = {
        "102,شامل": "102", //done
        "تأمين الزامي,شامل,طرف ثالث": "102", //done
        "تأمين الزامي,طرف ثالث": "101", //done
        "تأمين الزامي,شامل": "107", //done
        "طرف ثالث,شامل": "106", //no
        "تأمين الزامي": "103", //done
        "طرف ثالث": "104", //done
        "شامل": "105", //doen
        "102": "101",
    };
    const key = selected.join(',');
    return combinationMap[key] ?? null;
}
$(document).ready(function() {
    $('#calc-btn').click(function(event) {
        event.preventDefault();
        let code = getInsuranceCode();
        $('input[name="details[policy_vechile_class_type]"]').val(code);

        console.log(code);

        var formData = {
            p_loc: 1,
            p_use: $('#used-type').val(),
            p_sp_use: $('#used-type-spc').val(),
            p_load: $('#waight').val(),
            p_cc: $('#engine-capacity-val').val(),
            p_si: $('#price-car').val(),
            p_prod_year: $('#date-prod').val(),
            // p_min:$('#insurance0').val(),
            p_min: code,
            access_token: $('meta[name="csrf-token"]').attr('content')
        };
        $.ajax({
            url: 'http://82.213.0.2:8000/calc-price',
            type: 'GET',
            data: formData,
            success: function(response) {
                var total = Number(response.act) + Number(response.tp);

                var comp = response.comp;
                var allsum = Number(comp) + Number(total);
                console.log(allsum);
                $('#fullcost').show();
                $('#insuranceAmount').val(total);
                $('#compValue').val(comp);
                $('#full').html(allsum);
                $('input[name="details[act_price]"]').val(response.act);
                $('input[name="details[tp_price]"]').val(response.tp);
                $('input[name="details[comp_price]"]').val(comp);
                $('input[name="total_cost"]').val(allsum);
                $("#totalcost").val(allsum);
            },
            error: function(xhr, status, error) {
                console.log(response);

                $('#responseMessage').html('<p>Error: ' + error + '</p>');
            }
        });
    });
});
document.getElementById('item_id').addEventListener('change', function() {
    let selectedId = this.value;
    if (!selectedId) return;
    fetch(`/fetch-vehicle-specs/${selectedId}`)
        .then(response => response.json())
        .then(data => {
            let specsDropdown = document.getElementById('used-type-spc');
            specsDropdown.innerHTML = ''; // clear previous options

            data.forEach(item => {
                let option = document.createElement('option');
                option.value = item.spu_use;
                option.text = item.pol_use_adesc;
                specsDropdown.appendChild(option);
            });
        });
});
document.getElementById('car-type').addEventListener('change', function() {
    let selectedId = this.value;
    console.log(selectedId);

    if (!selectedId) return;
    fetch(`/fetch-vehicle-model/${selectedId}`)
        .then(response => response.json())
        .then(data => {
            let specsDropdown = document.getElementById('car-model');
            specsDropdown.innerHTML = ''; // clear previous options

            data.forEach(item => {
                let option = document.createElement('option');
                option.value = item.mot_brand_code;
                option.text = item.mot_brand_a;
                specsDropdown.appendChild(option);
            });
        });
});
//display data depone on location
document.addEventListener('DOMContentLoaded', function() {
    const selector = document.getElementById('location');
    const unifiedSection = document.getElementById('insurance-unified');
    const detailedSection = document.getElementById('insurance-detailed');

    function toggleSections() {
        if (selector.value === 'pal') {
            unifiedSection.classList.remove('d-none');
            detailedSection.classList.add('d-none');
        } else {
            unifiedSection.classList.add('d-none');
            detailedSection.classList.remove('d-none');
        }
    }
    selector.addEventListener('change', toggleSections);
    // ✅ نفذ عند تحميل الصفحة
    toggleSections();
});

// Function to show the image preview when an image is selected
function previewImage(event, index) {
    const file = event.target.files[0];
    const preview = document.getElementById('preview-' + index);

    // Check if the selected file is an image
    if (file && file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'inline'; // Show the preview
        }
        reader.readAsDataURL(file);
    } else {
        preview.style.display = 'none'; // Hide preview if not an image
    }
}

//complete button will show the complete form
function CompleteForms() {
    const section = document.getElementById('complet-section');
    section.style.display = 'block';
}
const typeSelect = document.getElementById("insurance-type");
const periodInput = document.getElementById("insurance-period");
const startDateInput = document.getElementById("startdate");
const endDateInput = document.getElementById("expiration-date");

// هذا القسم يُفعّل ويضبط الفترة حسب نوع المدة
typeSelect.addEventListener("change", function() {
    const selected = this.value.trim();
    console.log("Selected:", selected);

    if (selected === "1") {
        periodInput.value = 1;
        periodInput.disabled = true;
        periodInput.removeAttribute("min");
        periodInput.removeAttribute("max");
    } else if (selected === "2") {
        periodInput.disabled = false;
        periodInput.value = '';
        periodInput.min = 1;
        periodInput.max = 12;
    } else if (selected === "3") {
        periodInput.disabled = false;
        periodInput.value = '';
        periodInput.min = 1;
        periodInput.max = 365;
    } else {
        periodInput.value = '';
        periodInput.disabled = true;
        periodInput.removeAttribute("min");
        periodInput.removeAttribute("max");
    }
});
// section calculate the end date insu
function calculateEndDate() {
    const startDate = new Date(startDateInput.value);
    const periodType = typeSelect.value;
    const period = parseInt(periodInput.value);

    if (!startDateInput.value || !periodType || isNaN(period)) {
        endDateInput.value = '';
        return;
    }

    let endDate = new Date(startDate);

    if (periodType === "1") {
        endDate.setFullYear(endDate.getFullYear() + 1);
    } else if (periodType === "2") {
        endDate.setMonth(endDate.getMonth() + period);
    } else if (periodType === "3") {
        endDate.setDate(endDate.getDate() + period);
    }

    endDateInput.value = endDate.toISOString().split('T')[0];
}
startDateInput.addEventListener("change", calculateEndDate);
periodInput.addEventListener("input", calculateEndDate);
// هذا القسم يضبط القيم المدخلة بشكل يدوي حسب الحد الأقصى
periodInput.addEventListener("input", function() {
    const selected = typeSelect.value.trim();
    const value = parseInt(this.value);
    if (isNaN(value) || value < 1) {
        this.value = '';
        return;
    }
    if (selected === "2" && value > 12) {
        this.value = 12;
    } else if (selected === "3" && value > 365) {
        this.value = 365;
    }
});

// Get the select element for 'نوع الترخيص' (used-type)
const usedTypeSelect = document.getElementById("item_id");

// Get the fields to show/hide
const engineCapacityField = document.getElementById("engine-capacity");
const weightField = document.getElementById("weight-field");
const passengerNumberField = document.getElementById("passenger-number");

// Listen for changes in the 'نوع الترخيص' select
usedTypeSelect.addEventListener("change", function() {
    // Hide all fields by default
    engineCapacityField.style.display = "none";
    weightField.style.display = "none";
    passengerNumberField.style.display = "none";

    // Show the relevant field based on selection
    const selectedValue = usedTypeSelect.value;

    if (selectedValue === "1") {
        // Show engine capacity for 'المركبات الخصوصية'
        engineCapacityField.style.display = "flex";
    } else if (selectedValue === "2") {
        // Show weight for 'المركبات التجارية والمعدات'
        weightField.style.display = "flex";
    } else if (selectedValue === "3" || selectedValue === "4") {
        // Show passenger number for 'مركبات نقل الركاب بالاجرة' or 'الباصات'
        passengerNumberField.style.display = "flex";
    }

});

function shouldShowAuthorizedDriversQuestion() {
    const license = document.getElementById('location').value;
    let insuranceType = null;
    // Find checked insurance type(s)
    document.querySelectorAll('input[name="insurance_type"]:checked').forEach((el) => {
        if (el.value === '105' || el.value === 'شامل') insuranceType = '105';
    });
    console.log(license);
    console.log(insuranceType);
    // Show if QUDS, or PAL+105
    if (license === 'quds' || (license === 'pal' && insuranceType === '105')) {
        return true;
    }
    return false;
}
function toggleAuthorizedDriversQuestion() {
    const show = shouldShowAuthorizedDriversQuestion();
    const qDiv = document.getElementById('authorized-drivers-question');
    if (show) {
    console.log("yerdsy");
        qDiv.style.display = '';
        // Make required
        qDiv.querySelectorAll('input[type="radio"]').forEach(i => i.required = true);
    } else {
        qDiv.style.display = 'none';
        // Uncheck and remove required
        qDiv.querySelectorAll('input[type="radio"]').forEach(i => {i.checked = false; i.required = false;});
    }
}
document.getElementById('location').addEventListener('change', toggleAuthorizedDriversQuestion);
document.querySelectorAll('input[name="insurance_type"]').forEach(function(el) {
    el.addEventListener('change', toggleAuthorizedDriversQuestion);
});
</script>
@endsection