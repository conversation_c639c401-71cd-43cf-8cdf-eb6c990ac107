@extends('layouts/wordpressLayout')

@section('title', 'تفاصيل طلب التامين')
<!-- Add jQuery from CDN (make sure this is ABOVE your script) -->

@section('content')
<!-- Basic Layout & Basic with Icons -->
<div class="row" style="">

    <form id="car-calc-form">
        @csrf
        <div class="col-xxl">
            <div class="card mb-6">
                <div class="card-header d-flex align-items-center justify-content-between">
                </div>
                <div class="card-body">

                    <div class="row mb-6">
                        <label class="col-sm-3 col-form-label" for="location">المنطقة / التعرفة</label>
                        <div class="col-sm-9">
                            <select class="form-select" name="details[policy_location]" id="location"
                                aria-label="Default select example">
                                <option value="pal"
                                    {{ $order?->vehicleInsuranceDetails?->policy_location == 'pal' ? 'selected' : '' }}>
                                    فلسطينية</option>
                                <option value="quds"
                                    {{ $order?->vehicleInsuranceDetails?->policy_location == 'quds' ? 'selected' : '' }}>
                                    القدس</option>
                            </select>
                        </div>
                    </div>
                    <!-- ✅ القسم الأول (يظهر فقط عند اختيار "فلسطينية") -->
                    <div class="row mb-6" id="insurance-unified">
                        <label class="col-sm-3 col-form-label">نوع التأمين</label>
                        <div class="col-sm-9">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="insurance_type" value="102"
                                    id="insurance0">
                                <label class="form-check-label" for="insurance0">
                                    تأمين الزامي و طرف ثالث (الموحد)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="insurance_type" value="شامل"
                                    id="insurance1">
                                <label class="form-check-label" for="insurance1">
                                    شامل
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-6 d-none" id="insurance-detailed">
                        <label class="col-sm-3 col-form-label">نوع التأمين</label>
                        <div class="col-sm-9">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="insurance_type"
                                    value="تأمين الزامي" id="insurance2">
                                <label class="form-check-label" for="insurance2">تأمين الزامي</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="insurance_type" value="شامل"
                                    id="insurance3">
                                <label class="form-check-label" for="insurance3">شامل</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="insurance_type" value="طرف ثالث"
                                    id="insurance4">
                                <label class="form-check-label" for="insurance4">طرف ثالث</label>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-6">
                        <label class="col-sm-3 col-form-label" for="used-type">نوع الترخيص</label>
                        <div class="col-sm-9">
                            <select name="details[policy_use]" id="item_id" class="form-select"
                                aria-label="Default select example">
                                @foreach($items as $item)

                                <option value="{{ $order?->vehicleInsuranceDetails?->policy_use }}">
                                    {{ $item['pol_use_adesc'] }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="row mb-6">
                        <label class="col-sm-3 col-form-label" for="used-type-spc">الاستعمال الخاص</label>
                        <div class="col-sm-9">
                            <select class="form-select" name="details[policy_spical_use]" id="used-type-spc"
                                aria-label="Default select example">
                                <option selected value="{{$order?->vehicleInsuranceDetails?->policy_spical_use}}">
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-6" id="engine-capacity">
                        <label class="col-sm-3 col-form-label" for="engine-capacity">مواصفات المركبة <br> (سعة
                            المحرك)</label>
                        <div class="col-sm-9">
                            <select class="form-select" name="details[policy_cc]" id="engine-capacity-val"
                                aria-label="Default select example">
                                <option value="1200"
                                    {{ $order?->vehicleInsuranceDetails?->policy_cc == '1200' ? 'selected' : '' }}>1200
                                </option>
                                <option value="1400"
                                    {{ $order?->vehicleInsuranceDetails?->policy_cc == '1400' ? 'selected' : '' }}>1400
                                </option>
                                <option value="1600"
                                    {{ $order?->vehicleInsuranceDetails?->policy_cc == '1600' ? 'selected' : '' }}>1600
                                </option>
                                <option value="1800"
                                    {{ $order?->vehicleInsuranceDetails?->policy_cc == '1800' ? 'selected' : '' }}>1800
                                </option>
                                <option value="2000"
                                    {{ $order?->vehicleInsuranceDetails?->policy_cc == '2000' ? 'selected' : '' }}>2000
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-6" id="weight-field">
                        <label class="col-sm-3 col-form-label" for="waight"> الوزرن بالطن</label>
                        <div class="col-sm-9">
                            <input type="text" id="waight" name=" details[policy_load]" class="form-control phone-mask"
                                aria-describedby="basic-default-phone"
                                vlaue="{{$order?->vehicleInsuranceDetails?->policy_load}}" />
                        </div>
                    </div>
                    <div class="row mb-6" id="passenger-number">
                        <label class="col-sm-3 col-form-label" for="number-passenger"> عدد الركاب</label>
                        <div class="col-sm-9">
                            <input type="text" name="details[policy_seats]" id="number-passenger"
                                class="form-control phone-mask" aria-describedby="basic-default-phone"
                                value="{{$order?->vehicleInsuranceDetails?->policy_seats}}" />
                        </div>
                    </div>
                    <div class="row mb-6">
                        <label class="col-sm-3 col-form-label" for="insurance-type">نوع فترة التأمين</label>
                        <div class="col-sm-9">
                            <select class="form-select" id="insurance-type" name="details[]"
                                aria-label="Default select example">
                                <option selected disabled>اختر</option>
                                <option value="">سنة</option>
                                <option value="">شهر</option>
                                <option value="">يوم</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-6">
                        <label class="col-sm-3 col-form-label" for="insurance-period">فترة التأمين المطلوبة</label>
                        <div class="col-sm-9">
                            <input type="number" id="insurance-period" class="form-control" />
                        </div>
                    </div>
                    <div class="row mb-6">
                        <label class="col-sm-3 col-form-label" for="basic-default-phone">
                            مبلغ التأمين<br><small>(سعر المركبة - شيكل)</small>
                        </label>
                        <div class="col-sm-9">
                            <input type="text" id="price-car" name=" details[policy_inurance_vechile_price]"
                                class="form-control phone-mask" aria-describedby="basic-default-phone"
                                value="{{ $order?->vehicleInsuranceDetails?->policy_inurance_vechile_price }}" />
                        </div>
                    </div>
                    <div class="row mb-6">
                        <label class="col-sm-3 col-form-label" for="basic-default-phone"> سنة الصنع</label>
                        <div class="col-sm-9">
                            <input type="text" id="date-prod" name=" details[policy_prod_year]"
                                class="form-control phone-mask" aria-describedby="basic-default-phone"
                                value="{{$order?->vehicleInsuranceDetails?->policy_prod_year}}" />
                        </div>
                    </div>
                    <!-- </form> -->
                </div>
            </div>
        </div>
        <!--  قسم حسبة التامين-->
        <div class="col-xxl">
            <div class="card shadow-sm">
                <div class="card-body text-center">
                    <h5 class="card-title mb-4">نتائج حساب الاقساط</h5>
                    <div class="mt-4">
                        <label class="col-sm-3 col-form-label">قسط التأمين الإلزامي و الطرف الثالث</label>
                        <input type="text" readonly class="form-control text-center border-primary fw-bold"
                            id="insuranceAmount" value="">
                    </div>
                    <div class="mt-4">
                        <label class="col-sm-3 col-form-label">قسط التأمين (الشامل)</label>
                        <input type="text" id='compValue' readonly
                            class="form-control text-center border-primary fw-bold"
                            value="{{$order?->vehicleInsuranceDetails?->comp_price}}">
                    </div>
                    <div class="mt-4">
                        <label class="col-sm-3 col-form-label">تكلفة التأمين</label>
                        <input type="text" id='full' readonly class="form-control text-center border-primary fw-bold"
                            value="{{$order->total_price }}">
                    </div>
                </div>
            </div>
        </div>
</div>

<!--user info section -->
<div class="col-xxl">
    <div class="card shadow-sm">
        <div class="card-body text-center">
            <h5 class="card-title mb-4">بيانات العميل </h5>
            <div class="row mb-6">
                <label class="col-sm-3 col-form-label" for="name">اسم المؤمن له </label>
                <div class="col-sm-9">
                    <input type="text" name="details[insured_full_name]" id="customer_name"
                        class="form-control phone-mask" aria-describedby="basic-default-phone"
                        value="{{auth()->user()->fullname}}" />
                </div>
            </div>
            <div class="row mb-6">
                <label class="col-sm-3 col-form-label" for="id">رقم هوية المؤمن له </label>
                <div class="col-sm-9">
                    <input type="text" name="details[insured_identity]" id="customer_id" class="form-control phone-mask"
                        aria-describedby="basic-default-phone" value="{{auth()->user()->identity}}" />
                </div>
            </div>
            <div class="row mb-6">
                <label class="col-sm-3 col-form-label" for="dob">تاريخ ميلاد المؤمن له </label>
                <div class="col-sm-9">
                    <input type="text" name="details[insured_date_of_birth]" id="customer_dob"
                        class="form-control phone-mask" aria-describedby="basic-default-phone"
                        value="{{auth()->user()->date_of_birth}}" />
                </div>
            </div>
            <div class="row mb-6">
                <label class="col-sm-3 col-form-label" for="email">البريد الالكتروني المؤمن له</label>
                <div class="col-sm-9">
                    <input type="text" name="details[insured_emai]" id="customer_email" class="form-control phone-mask"
                        aria-describedby="basic-default-phone" value="{{auth()->user()->email}}" />
                </div>
            </div>
            <div class="row mb-6">
                <label class="col-sm-3 col-form-label" for="phone">رقم هاتف المؤمن له </label>
                <div class="col-sm-9">
                    <input type="text" name="details[insured_phone]" id="customer_phone" class="form-control phone-mask"
                        aria-describedby="basic-default-phone" value="{{auth()->user()->mobile}}" />
                </div>
            </div>

        </div>
    </div>
</div>
</div>
<input type="hidden" id="type_id" name="type_id" value="1">
<input type="hidden" id="policy_vechile_class_type" name="policy_vechile_class_type" value="">
<input type="hidden" id="act_price" name="details[act_price]" value="">
<input type="hidden" id="tp_price" name="details[tp_price]" value="">
<input type="hidden" id="comp_price" name="details[comp_price]" value="">
<input type="hidden" id="total_cost" name="total_cost" value="">
<!-- قسم التكملة للطلب -->
<div class="row">
    <div class="col-xxl" id='complet-section'>
        <div class="card mb-6">
            <div class="card-header d-flex align-items-center justify-content-between">
            </div>
            <div class="card-body">
                <!-- <form id="complet-form"> -->
                <div class="row mb-6">
                    <label class="col-sm-3 col-form-label" for="start-insu"> بداية التأمين</label>
                    <div class="col-sm-9">
                        <input disabled type="date" name="details[start_insurance_date]" id="startdate"
                            class="form-control phone-mask" aria-describedby="basic-default-phone"
                            value="{{$order?->vehicleInsuranceDetails?->start_insurance_date}}" />
                    </div>
                </div>
                <div class="row mb-6">
                    <label class="col-sm-3 col-form-label" for="end-insu"> نهاية التأمين</label>
                    <div class="col-sm-9">
                        <input disabled type="date" name="details[end_insurance_date]" id="expiration-date"
                            class="form-control phone-mask" aria-describedby="basic-default-phone"
                            value="{{$order?->vehicleInsuranceDetails?->end_insurance_date}}" />
                    </div>
                </div>
                <div class="row mb-6">
                    <label class="col-sm-3 col-form-label" for="calc">مجموع القسط</label>
                    <div class="col-sm-9">
                        <input disabled type="text" id="sum" class="form-control phone-mask"
                            aria-describedby="basic-default-phone"
                            value="{{$order->total_price }}" />
                    </div>
                </div>
                <div class="row mb-6">
                    <label class="col-sm-3 col-form-label" for="chase-number"> رقم الشصي </label>
                    <div class="col-sm-9">
                        <input type="text" name="details[chasi_number]" id="chase" class="form-control phone-mask"
                            aria-describedby="basic-default-phone"
                            value="{{$order?->vehicleInsuranceDetails?->chasi_number}}" />
                    </div>
                </div>

                <div class="row mb-6">
                    <label class="col-sm-3 col-form-label" for="name"> اسماء المخولين في القيادة</label>
                    <div class="col-sm-9">
                        <input type="text" name="details[who_can_drive_name]" id="namecandrive"
                            class="form-control phone-mask" aria-describedby="basic-default-phone"
                            value="{{$order?->vehicleInsuranceDetails?->who_can_drive_name}}" />
                    </div>
                </div>
                <div class="row mb-6">
                    <label class="col-sm-3 col-form-label" for="idcandrive"> ارقام هويات المخولين بالقيادة</label>
                    <div class="col-sm-9">
                        <input type="text" name="details[who_can_drive_identity]" id="idcandrive"
                            class="form-control phone-mask" aria-describedby="basic-default-phone"
                            vlaue="{{$order?->vehicleInsuranceDetails?->who_can_drive_identity}}" />
                    </div>
                </div>
                <div class="row mb-6">
                    <label class="col-sm-3 col-form-label" for="car-numver"> رقم لوحة المركبة </label>
                    <div class="col-sm-9">
                        <input type="text" id="car-number" name="details[plate_number]" class="form-control phone-mask"
                            aria-describedby="basic-default-phone"
                            value="{{$order?->vehicleInsuranceDetails?->plate_number}}" />
                    </div>
                </div>
                <div class="row mb-6">
                    <label class="col-sm-3 col-form-label" for="car-type"> نوع المركبة </label>
                    <div class="col-sm-9">
                        <select class="form-select" name="details[vechile_type]" id="car-type"
                            aria-label="Default select example">
                            <option value="siat"
                                {{ $order?->vehicleInsuranceDetails?->vechile_type == 'siat' ? 'selected' : '' }}>سيات
                            </option>
                            <option value="bmw"
                                {{ $order?->vehicleInsuranceDetails?->vechile_type == 'bmw' ? 'selected' : '' }}>بي ام
                            </option>
                            <option value="od"
                                {{ $order?->vehicleInsuranceDetails?->vechile_type == 'od' ? 'selected' : '' }}>اودي
                            </option>
                        </select>
                    </div>
                </div>
                <div class="row mb-6">
                    <label class="col-sm-3 col-form-label" for="desiel-type"> نوع الوقود </label>
                    <div class="col-sm-9">
                        <select class="form-select" name="details[feul_type]" id="desiel-type"
                            aria-label="Default select example">
                            <option value="1"
                                {{ $order?->vehicleInsuranceDetails?->feul_type == '1' ? 'selected' : '' }}>بنزين
                            </option>
                            <option value="2"
                                {{ $order?->vehicleInsuranceDetails?->feul_type == '2' ? 'selected' : '' }}>سولار
                            </option>
                            <option value="3"
                                {{ $order?->vehicleInsuranceDetails?->feul_type == '3' ? 'selected' : '' }}>كهرباء
                            </option>
                        </select>
                    </div>
                </div>
                <div class="row mb-6">
                    <label class="col-sm-3 col-form-label">صور المركبة (7 صور)</label>
                    <div class="col-sm-9">
                    @foreach ([
                        'left_image' => 'Left Image',
                        'right_image' => 'Right Image',
                        'back_image' => 'Back Image',
                        'front_image' => 'Front Image',
                        'driver_license_image' => 'Driver License',
                        'chasi_image' => 'Chassis Image',
                        'identitiy_image' => 'Identity Image',
                        'vechile_llicense_image' => 'Vehicle License Image'
                    ] as $field => $label)
                        @php
                            $image = $order?->vehicleInsuranceDetails[$field] ?? null;
                        @endphp
                        @if ($image)
                        <div class="text-center">
                            <img src="{{ showImage($image) }}" alt="{{ $label }}" width="300" style="border:1px solid #ccc; padding:5px;">
                            <div style="margin-top: 5px; font-size: 14px; color: #333;">
                                {{ $image ? $label : 'No Image' }}
                            </div>
                        </div>

                            <!-- <img src="{{ showImage($image) }}" alt="{{ $label }}" width="200"> -->

                        @endif
                    @endforeach
                    </div>
                </div>
        </div>
    </div>
</div>
</div>

</form>
@endsection
@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('input, button, select, textarea').forEach(el => el.disabled = true);

    const actPrice = parseFloat("{{ $order?->vehicleInsuranceDetails?->act_price ?? 0 }}");
    const tpPrice = parseFloat("{{ $order?->vehicleInsuranceDetails?->tp_price ?? 0 }}");
    const total = actPrice + tpPrice;
    document.getElementById('insuranceAmount').value = total.toFixed(2);

    const selector = document.getElementById('location');
    const unifiedSection = document.getElementById('insurance-unified');
    const detailedSection = document.getElementById('insurance-detailed');

    function toggleSections() {
        if (selector.value === 'pal') {
            unifiedSection.classList.remove('d-none');
            detailedSection.classList.add('d-none');
        } else {
            unifiedSection.classList.add('d-none');
            detailedSection.classList.remove('d-none');
        }
    }
    selector.addEventListener('change', toggleSections);
    // ✅ نفذ عند تحميل الصفحة
    toggleSections();
    const policyValue = "{{ $order?->vehicleInsuranceDetails?->policy_vechile_class_type }}"
        .toString(); // زي: "102"
    console.log(policyValue);


    const reverseMap = {
        "102": ["102", "شامل"],
        "101": ["تأمين الزامي", "طرف ثالث"],
        "107": ["تأمين الزامي", "شامل"],
        "106": ["طرف ثالث", "شامل"],
        "103": ["تأمين الزامي"],
        "104": ["طرف ثالث"],
        "105": ["شامل"],
    };

    const valuesToCheck = reverseMap[policyValue] || [];
    console.log(valuesToCheck);
    // شيك على الشيكبوكسات المناسبة
    valuesToCheck.forEach(value => {
        const checkbox = document.querySelector(`input[type="checkbox"][value="${value}"]`);
        if (checkbox) checkbox.checked = true;
    });

    // أظهر الديف المناسب
    if (policyValue === "102" || policyValue === "101") {
        document.getElementById("insurance-unified").classList.remove("d-none");
        document.getElementById("insurance-detailed").classList.add("d-none");
    } else {
        document.getElementById("insurance-unified").classList.add("d-none");
        document.getElementById("insurance-detailed").classList.remove("d-none");
    }
});
</script>
@endsection