@extends('layouts/wordpressLayout')

@section('title', 'حاسبة قسط تأمين المصاعد')

{{-- Required CSS --}}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" />

@section('page-style')
<style>
    :root {
        --primary-color: #003D4C;
        --secondary-color: #0094B3;
        --border-color: #e2e8f0;
        --bg-hover: rgba(0, 148, 179, 0.05);
    }

    .form-section {
        background: #fff;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.05);
        padding: 2rem;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }

    .form-section:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .section-title {
        color: var(--primary-color);
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--border-color);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-title i {
        color: var(--secondary-color);
        font-size: 1.75rem;
    }

    .form-control, .form-select {
        border-radius: 0px !important;
        border: 1.5px solid var(--border-color);
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
        background-color: #f8fafc;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--secondary-color);
        box-shadow: 0 0 0 3px rgba(0, 148, 179, 0.1);
        background-color: #fff;
    }

    .input-group-text {
        background: #f8fafc;
        border: 1.5px solid var(--border-color);
        border-radius: 10px;
        color: var(--secondary-color);
    }

    .form-label {
        font-weight: 500;
        color: #4a5568;
        margin-bottom: 0.5rem;
    }

    .btn-primary {
        background: var(--primary-color);
        border: none;
        padding: 0.8rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
        color: white;
    }

    .btn-primary:hover {
        background: var(--secondary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .form-check {
        padding: 0.75rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        margin-bottom: 0.5rem;
        cursor: pointer;
    }

    .form-check:hover {
        background: var(--bg-hover);
    }

    .form-check-input:checked {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .price-display {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--primary-color);
        text-align: center;
        margin: 1.5rem 0;
        text-shadow: 0 2px 4px rgba(0, 61, 76, 0.1);
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255,255,255,0.9);
        backdrop-filter: blur(5px);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .submit-section {
        text-align: center;
        margin-top: 3rem;
        padding: 2rem;
        background: #fff;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.05);
    }

    .btn-submit {
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 1rem 3rem;
        border-radius: 10px;
        font-weight: 600;
        font-size: 1.1rem;
        min-width: 200px;
        transition: all 0.3s ease;
    }

    .btn-submit:hover {
        background: var(--secondary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .btn-submit:disabled {
        background: #cbd5e1;
        cursor: not-allowed;
        transform: none;
    }

    .select2-container--classic .select2-selection--single {
        height: calc(2.5rem + 2px) !important;
        border: 1.5px solid var(--border-color) !important;
        background: #f8fafc !important;
    }

    .select2-container--classic .select2-selection--single:focus {
        border-color: var(--secondary-color) !important;
        box-shadow: 0 0 0 3px rgba(0, 148, 179, 0.1) !important;
    }

    .select2-container--classic .select2-selection--single .select2-selection__rendered {
        line-height: calc(2.5rem + 2px) !important;
        color: #4a5568 !important;
    }

    .select2-container--classic .select2-selection--single .select2-selection__arrow {
        height: calc(2.5rem + 2px) !important;
        background: var(--primary-color) !important;
        border: none !important;
        border-radius: 0 10px 10px 0 !important;
        width: 30px !important;
    }

    .select2-container--classic .select2-selection--single .select2-selection__arrow b {
        border-color: #fff transparent transparent transparent !important;
    }

    /* RTL Specific Styles */
    .rtl {
        direction: rtl;
        text-align: right;
    }

    .rtl .input-group > .form-control {
        border-radius: 10px 0 0 10px;
    }

    .rtl .input-group > .input-group-text {
        border-radius: 0 10px 10px 0;
    }

    .rtl .me-2 {
        margin-left: 0.5rem !important;
        margin-right: 0 !important;
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .form-section {
            padding: 1.5rem;
        }

        .section-title {
            font-size: 1.25rem;
        }

        .price-display {
            font-size: 2rem;
        }

        .btn-submit {
            width: 100%;
        }
    }

    .card {
        background: #fff;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.05);
        margin-bottom: 2rem;
        border: none;
        overflow: hidden;
    }

    .card-body {
        padding: 2rem;
    }

    .card-header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 1.5rem 2rem;
        border: none;
    }

    .mb-6 {
        margin-bottom: 1.5rem;
    }

    .row {
        margin-bottom: 1.5rem;
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <form id="elevator-calc-form" action="{{ route('insurance-orders.store') }}" method="POST">
        @csrf
        <input type="hidden" id="type_id" name="type_id" value="5">
        
        <!-- Elevator Information Section -->
        <div class="form-section">
            <h5 class="section-title">
                <i class='bx bx-building-house'></i>
                معلومات المصعد
            </h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-6">
                        <label class="form-label" for="start-insu">بداية التأمين</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-calendar'></i></span>
                            <input type="date" name="details[p_ins_start_dt]" id="startdate" class="form-control" />
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-6">
                        <label class="form-label" for="end-insu">نهاية التأمين</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-calendar'></i></span>
                            <input type="date" name="details[p_ins_end_dt]" id="expiration-date" class="form-control" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-6">
                        <label class="form-label" for="location">الفرع</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-map'></i></span>
                            <select class="form-select" name="details[location]" id="location">
                                <option value="select">اختر</option>
                                @foreach($locations as $location)
                                <option value="{{$location['travel_to_code']}}">{{$location['travel_to_sname_a']}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-6">
                        <label class="form-label" for="p_elevator_type">نوع المصعد</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-elevator'></i></span>
                            <select class="form-select" name="details[p_elevator_type]" id="p_elevator_type">
                                @foreach($elevatorcategory as $type)
                                <option value="{{$type['elev_cat']}}">{{$type['elev_cat_aname']}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-6">
                        <label class="form-label" for="coverage_ceiling">سقف التامين</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-money'></i></span>
                            <select class="form-select" name="details[coverage_ceiling]" id="coverage_ceiling">
                                <option value="1">100000</option>
                                <option value="2">250000</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-6">
                        <label class="form-label" for="waight">الحمولة</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-weight'></i></span>
                            <input type="text" id="waight" name="details[policy_load]" class="form-control" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="mb-6">
                        <label class="form-label" for="p_prod_year">سنة الصنع</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-calendar'></i></span>
                            <input type="text" id="p_prod_year" name="details[p_prod_year]" class="form-control" />
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-6">
                        <label class="form-label" for="p_no_passengers">عدد الركاب</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-group'></i></span>
                            <input type="text" id="p_no_passengers" name="details[p_no_passengers]" class="form-control" />
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-6">
                        <label class="form-label" for="p_no_floors">عدد المحطات</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-buildings'></i></span>
                            <select class="form-select" name="details[p_no_floors]" id="p_no_floors">
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-end mt-4">
                <button type="button" class="btn btn-primary" id="calc-btn">
                    <i class='bx bx-calculator me-2'></i>
                    احسب القسط
                </button>
            </div>
        </div>

        <!-- Premium Calculation Results -->
        <div class="form-section">
            <h5 class="section-title">
                <i class='bx bx-money'></i>
                نتائج حساب الاقساط
            </h5>
            <div class="text-center">
                <div class="mb-6">
                    <label class="form-label">قسط التأمين</label>
                    <input type="text" id='compValue' readonly class="form-control text-center" value="">
                </div>
                <button onclick="CompleteForms()" type="button" class="btn btn-primary">
                    <i class='bx bx-check-circle me-2'></i>
                    تكملة الطلب
                </button>
            </div>
        </div>

        <!-- Customer Information Section -->
        <div class="form-section" id="complet-section" style="display: none;">
            <h5 class="section-title">
                <i class='bx bx-user'></i>
                بيانات العميل
            </h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-6">
                        <label class="form-label" for="insured_full_name">اسم المؤمن له</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-user'></i></span>
                            <input type="text" name="details[insured_full_name]" id="insured_full_name" class="form-control" value="{{auth()->user()->fullname}}" />
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-6">
                        <label class="form-label" for="customer_identity">رقم هوية المؤمن له</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-id-card'></i></span>
                            <input type="text" name="details[customer_identity]" id="customer_identity" class="form-control" value="{{auth()->user()->identity}}" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-6">
                        <label class="form-label" for="insured_date_of_birth">تاريخ ميلاد المؤمن له</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-calendar'></i></span>
                            <input type="date" name="details[insured_date_of_birth]" id="insured_date_of_birth" class="form-control" value="{{auth()->user()->date_of_birth}}" />
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-6">
                        <label class="form-label" for="insured_email">البريد الالكتروني المؤمن له</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-envelope'></i></span>
                            <input type="email" name="details[insured_email]" id="insured_email" class="form-control" value="{{auth()->user()->email}}" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-6">
                        <label class="form-label" for="insured_phone">رقم هاتف المؤمن له</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-phone'></i></span>
                            <input type="tel" name="details[insured_phone]" id="insured_phone" class="form-control" value="{{auth()->user()->mobile}}" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Building Information -->
            <h5 class="section-title mt-4">
                <i class='bx bx-building'></i>
                معلومات المبنى
            </h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-6">
                        <label class="form-label" for="building_name">اسم العمارة</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-building-house'></i></span>
                            <input type="text" id="building_name" name="details[building_name]" class="form-control" />
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-6">
                        <label class="form-label" for="building_type">تصنيف المبنى</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-buildings'></i></span>
                            <select class="form-select" name="details[building_type]" id="building_type">
                                <option value="select">اختر</option>
                                @foreach($buildingcategory as $type)
                                <option value="{{$type['elev_buid_cat']}}">{{$type['elev_buid_cat_aname']}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="mb-6">
                        <label class="form-label" for="full_address">العنوان</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-map-pin'></i></span>
                            <input type="text" name="details[full_address]" id="full_address" class="form-control" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="mb-6">
                        <label class="form-label" for="city">المدينة</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-city'></i></span>
                            <select class="form-select" name="details[city]" id="city">
                                <option value="select">اختر</option>
                                @foreach($locations as $location)
                                <option value="{{$location['travel_to_code']}}">{{$location['travel_to_sname_a']}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-6">
                        <label class="form-label" for="street">الشارع</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-street-view'></i></span>
                            <input type="text" name="details[street]" id="street" class="form-control" />
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-6">
                        <label class="form-label" for="building_number">رقم المبنى</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class='bx bx-hash'></i></span>
                            <input type="text" name="details[building_number]" id="building_number" class="form-control" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Section -->
        <div class="submit-section">
            <button type="submit" class="btn btn-submit">
                <i class='bx bx-check-circle me-2'></i>
                حفظ الطلب
            </button>
            <p class="text-muted mt-3">
                بالضغط على زر الإرسال، أنت توافق على الشروط والأحكام
            </p>
        </div>
    </form>

    <div class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function CompleteForms() {
    const section = document.getElementById('complet-section');
    section.style.display = 'block';
}

$(document).ready(function() {
    $('#calc-btn').click(function(event) {
        event.preventDefault();
        // Show loading overlay
        $('.loading-overlay').css('display', 'flex');
        
        var formData = {
            p_elevator_type: $('#p_elevator_type').val(),
            p_no_passengers: $('#p_no_passengers').val(),
            p_prod_year: $('#p_prod_year').val(),
            p_no_floors: $('#p_no_floors').val(),
            coverage_ceiling: $('#coverage_ceiling').val(),
            access_token: $('meta[name="csrf-token"]').attr('content')
        };

        $.ajax({
            url: 'http://**********:8000/elevator-prem',
            type: 'GET',
            data: formData,
            success: function(response) {
                // Hide loading overlay
                $('.loading-overlay').hide();
                
                $('#compValue').val(response.premium);
                $('input[name="total_cost"]').val(response.premium);
            },
            error: function(xhr, status, error) {
                // Hide loading overlay
                $('.loading-overlay').hide();
                
                console.error(error);
                alert('حدث خطأ أثناء حساب القسط');
            }
        });
    });

    // Initialize Select2
    $('.form-select').select2({
        theme: 'classic',
        dir: 'rtl',
        language: 'ar'
    });
});
</script>
@endsection