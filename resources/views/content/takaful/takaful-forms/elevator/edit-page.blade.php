@extends('layouts/wordpressLayout')

@section('title', ' حاسبة قسط تأمين المصاعد')
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

@section('content')
<!-- Basic Layout & Basic with Icons -->
<div class="row" style="">
    <!-- Basic Layout -->
    <h5 class="mb-0">حاسبة قسط تأمين المصاعد </h5>
    <p class="text-muted float-end">توفر شركة التكافل أداة ذكية وسهلة الاستخدام لحساب قسط تأمين مركبتك بدقة وسرعة. تتيح
        لك الحاسبة إدخال معلومات المركبة الأساسية مثل نوع المركبة، سنة الصنع، سعة المحرك، ونوع التأمين المطلوب (شامل أو
        ضد الغير)، لتحصل فورًا على تقدير تقريبي لقيمة القسط التأميني.

        تهدف هذه الخدمة إلى مساعدة العملاء على اتخاذ قرارات مالية مدروسة ومعرفة التكاليف قبل شراء بوليصة التأمين، بكل
        شفافية وسلاسة.</p>
    <form id="car-calc-form" action="{{ route('insurance-orders.store') }}" method="POST">
        @csrf
        <div class="col-xxl">
            <div class="card mb-6">
                <div class="card-header d-flex align-items-center justify-content-between">
                </div>
                <div class="card-body">
                    <div class="row mb-6">
                        <label class="col-sm-3 col-form-label" for="start-insu"> بداية التأمين</label>
                        <div class="col-sm-9">
                            <input type="date" name="details[start_insurance_date]" id="startdate"
                                class="form-control phone-mask" aria-describedby="basic-default-phone"
                                value="{{$order?->ElevatorInsuranceDetails?->start_insurance_date}}" />
                        </div>
                    </div>
                    <div class="row mb-6">
                        <label class="col-sm-3 col-form-label" for="end-insu"> نهاية التأمين</label>
                        <div class="col-sm-9">
                            <input type="date" name="details[end_insurance_date]" id="expiration-date"
                                class="form-control phone-mask" aria-describedby="basic-default-phone"
                                value="{{$order?->ElevatorInsuranceDetails?->end_insurance_date}}" />
                        </div>
                    </div>
                    <div class="row mb-6">
                        <label class="col-sm-3 col-form-label" for="location">الفرع</label>
                        <div class="col-sm-9">
                            <select class="form-select" name="details[policy_location]" id="location"
                                aria-label="Default select example">
                                <option value="select">اختر</option>
                                @foreach($locations as $location)
                                <option value="{{$location['travel_to_code']}}"
                                    {{$order?->ElevatorInsuranceDetails?->policy_location == $location['travel_to_code'] ? 'selected' : ''}}>
                                    {{$location['travel_to_sname_a']}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="row mb-6">
                        <label class="col-sm-3 col-form-label" for="p_elevator_type">نوع المصعد</label>
                        <div class="col-sm-9">
                            <select class="form-select" name="details[p_elevator_type]" id="p_elevator_type"
                                aria-label="Default select example">
                                @foreach($elevatorcategory as $type)
                                <option value="{{$type['elev_cat']}}"
                                    {{$order?->ElevatorInsuranceDetails?->p_elevator_type == $type['elev_cat'] ? 'selected' : ''}}>
                                    {{$type['elev_cat_aname']}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="row mb-6">
                        <label class="col-sm-3 col-form-label" for="coverage_ceiling">سقف التامين</label>
                        <div class="col-sm-9">
                            <select class="form-select" name="details[coverage_ceiling]" id="coverage_ceiling"
                                aria-label="Default select example">
                                <option selected value="1">100000</option>
                                <option value="2">250000</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-6" id="weight-field">
                        <label class="col-sm-3 col-form-label" for="waight">الحمولة</label>
                        <div class="col-sm-9">
                            <input type="text" id="waight" name=" details[policy_load]" class="form-control phone-mask"
                                aria-describedby="basic-default-phone"
                                value="{{$order?->ElevatorInsuranceDetails?->start_insurance_date}}" />
                        </div>
                    </div>
                    <div class="row mb-6">
                        <label class="col-sm-3 col-form-label" for="p_prod_year"> سنة الصنع</label>
                        <div class="col-sm-9">
                            <input value="{{$order?->ElevatorInsuranceDetails?->p_prod_year}}" type="text"
                                id="p_prod_year" name=" details[p_prod_year]" class="form-control phone-mask"
                                aria-describedby="basic-default-phone" />
                        </div>
                    </div>
                    <div class="row mb-6">
                        <label class="col-sm-3 col-form-label" for="p_no_passengers"> عدد الركاب</label>
                        <div class="col-sm-9">
                            <input value="{{$order?->ElevatorInsuranceDetails?->p_no_passengers}}" type="text"
                                id="p_no_passengers" name=" details[p_no_passengers]" class="form-control phone-mask"
                                aria-describedby="basic-default-phone" />
                        </div>
                    </div>
                    <div class="row mb-6">
                        <label class="col-sm-3 col-form-label" for="p_no_floors"> عدد المحطات</label>
                        <div class="col-sm-9">
                            <select class="form-select" name="details[p_no_floors]" id="p_no_floors"
                                aria-label="Default select example">
                                <option selected value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                            </select>
                        </div>
                        <div class="row justify-content-end">
                            <div class="col-sm-10">
                                <button type="button" class="btn btn-primary" id="calc-btn">احسب</button>
                            </div>
                        </div>
                        <div id="responseMessage" class="mt-3 text-success"></div>
                        <!-- </form> -->
                    </div>
                </div>
            </div>
            <!--  قسم حسبة التامين-->
            <div class="col-xxl">
                <div class="card shadow-sm">
                    <div class="card-body text-center">
                        <h5 class="card-title mb-4">نتائج حساب الاقساط</h5>
                        <div class="mt-4">
                            <label class="col-sm-3 col-form-label">قسط التأمين</label>
                            <input type="text" id='compValue' readonly
                                class="form-control text-center border-primary fw-bold" value="">
                        </div>
                        <div class="row justify-content-end mt-4">
                            <div class="col-sm-10">
                                <button onclick="CompleteForms()" type="button" class="btn btn-primary">تكملة
                                    الطلب</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--user info section -->
            <div class="col-xxl">
                <div class="card shadow-sm">
                    <div class="card-body text-center">
                        <h5 class="card-title mb-4">بيانات العميل </h5>
                        <div class="row mb-6">
                            <label class="col-sm-3 col-form-label" for="insured_full_name">اسم المؤمن له </label>
                            <div class="col-sm-9">
                                <input type="text" name="details[insured_full_name]" id="insured_full_name"
                                    class="form-control phone-mask" aria-describedby="basic-default-phone"
                                    value="{{auth()->user()->fullname}}" />
                            </div>
                        </div>
                        <div class="row mb-6">
                            <label class="col-sm-3 col-form-label" for="id">رقم هوية المؤمن له </label>
                            <div class="col-sm-9">
                                <input type="text" name="details[customer_identity]" id="customer_identity"
                                    class="form-control phone-mask" aria-describedby="basic-default-phone"
                                    value="{{auth()->user()->identity}}" />
                            </div>
                        </div>
                        <div class="row mb-6">
                            <label class="col-sm-3 col-form-label" for="dob">تاريخ ميلاد المؤمن له </label>
                            <div class="col-sm-9">
                                <input type="text" name="details[insured_date_of_birth]" id="insured_date_of_birth"
                                    class="form-control phone-mask" aria-describedby="basic-default-phone"
                                    value="{{auth()->user()->date_of_birth}}" />
                            </div>
                        </div>
                        <div class="row mb-6">
                            <label class="col-sm-3 col-form-label" for="email">البريد الالكتروني المؤمن له</label>
                            <div class="col-sm-9">
                                <input type="text" name="details[insured_email]" id="insured_email"
                                    class="form-control phone-mask" aria-describedby="basic-default-phone"
                                    value="{{auth()->user()->email}}" />
                            </div>
                        </div>
                        <div class="row mb-6">
                            <label class="col-sm-3 col-form-label" for="phone">رقم هاتف المؤمن له </label>
                            <div class="col-sm-9">
                                <input type="text" name="details[insured_phone]" id="insured_phone"
                                    class="form-control phone-mask" aria-describedby="basic-default-phone"
                                    value="{{auth()->user()->mobile}}" />
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <input type="hidden" id="type_id" name="type_id" value="5">
        <input type="hidden" id="policy_vechile_class_type" name="details[policy_vechile_class_type]" value="">
        <input type="hidden" id="act_price" name="details[act_price]" value="">
        <input type="hidden" id="tp_price" name="details[tp_price]" value="">
        <input type="hidden" id="comp_price" name="details[comp_price]" value="">
        <input type="hidden" id="total_cost" name="total_cost" value="">
        <!-- قسم التكملة للطلب -->
        <div class="row">
            <div class="col-xxl" id='complet-section' style="display: none;">
                <div class="card mb-6">
                    <div class="card-header d-flex align-items-center justify-content-between">
                    </div>
                    <div class="card-body">
                        <!-- <form id="complet-form"> -->

                        <div class="row mb-6">
                            <label class="col-sm-3 col-form-label" for="building_name">اسم العمارة</label>
                            <div class="col-sm-9">
                                <input value="{{$order?->ElevatorInsuranceDetails?->building_name}}" type="text"
                                    id="building_name" name="details[building_name]" class="form-control phone-mask"
                                    aria-describedby="basic-default-phone" />
                            </div>
                        </div>
                        <div class="row mb-6">
                            <label class="col-sm-3 col-form-label" for="building_type">تصنيف المبنى</label>
                            <div class="col-sm-9">
                                <select class="form-select" name="details[building_type]" id="building_type"
                                    aria-label="Default select example">
                                    <option value="select">اختر</option>
                                    @foreach($buildingcategory as $type)
                                    <option value="{{$type['elev_buid_cat']}}"
                                        {{$order?->ElevatorInsuranceDetails?->building_type == $type['elev_buid_cat'] ? 'selected' : ''}}>
                                        {{$type['elev_buid_cat_aname']}}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="row mb-6">
                            <label class="col-sm-3 col-form-label" for="full_address">العنوان</label>
                            <div class="col-sm-9">
                                <input value="{{$order?->ElevatorInsuranceDetails?->full_address}}" type="text"
                                    name="details[full_address]" id="full_address" class="form-control phone-mask"
                                    aria-describedby="basic-default-phone" />
                            </div>
                        </div>
                        <div class="row mb-6">
                            <label class="col-sm-3 col-form-label" for="city">المدينة</label>
                            <div class="col-sm-9">
                                <select class="form-select" name="details[city]" id="city"
                                    aria-label="Default select example">
                                    <option value="select">اختر</option>
                                    @foreach($locations as $location)
                                    <option value="{{$location['travel_to_code']}}"
                                        {{$order?->ElevatorInsuranceDetails?->city == $location['travel_to_code'] ? 'selected' :''}}>
                                        {{$location['travel_to_sname_a']}}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="row mb-6">
                            <label class="col-sm-3 col-form-label" for="street">الشارع</label>
                            <div class="col-sm-9">
                                <input value="{{$order?->ElevatorInsuranceDetails?->street}"  type="text" name="details[street]" id="street" class="form-control phone-mask"  aria-describedby="basic-default-phone" />
            </div>
          </div>
          <div class="row mb-6">
            <label class="col-sm-3 col-form-label" for="building_number">رقم المبنى</label>
            <div class="col-sm-9">
              <input value="{{$order?->ElevatorInsuranceDetails?->building_number}"   type="text" name="details[building_number]" id="building_number" class="form-control phone-mask"  aria-describedby="basic-default-phone" />
            </div>
          </div>
           <div class="card-body">
            <div class="row mb-6">
            <label class="col-sm-3 col-form-label" for="start-insu"> بداية التأمين</label>
            <div class="col-sm-9">
              <input type="date" value="{{$order?->ElevatorInsuranceDetails?->start_insurance_date}" name="details[start_insurance_date]" id="startdate" class="form-control phone-mask"  aria-describedby="basic-default-phone" />
            </div>
          </div>
          <div class="row mb-6">
            <label class="col-sm-3 col-form-label" for="end-insu"> نهاية التأمين</label>
            <div class="col-sm-9">
              <input value="{{$order?->ElevatorInsuranceDetails?->start_insurance_date}"  type="date" name="details[end_insurance_date]" id="expiration-date" class="form-control phone-mask"  aria-describedby="basic-default-phone" />
            </div>
          </div>
                      </div>

        </div>
   </div>
    </div>
  </div>
  </div>
  <div class="row justify-content-end mt-4"  >
    <div class="col-sm-10">
      <button type="submit"   class="btn btn-primary"> حفظ الطلب </button>
    </div>
  </div>
</form>
@endsection
@section('scripts')
<script>
    document.querySelectorAll('input, button, select, textarea').forEach(el => el.disabled = true);

    function CompleteForms() {
    const section = document.getElementById('complet-section');
    section.style.display = 'block';
  }
    </script>
@endsection