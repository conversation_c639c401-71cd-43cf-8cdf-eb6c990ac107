@extends('layouts/wordpressLayout')

@section('title', 'تأمين الأفراد')
<!-- Add jQuery from CDN (make sure this is ABOVE your script) -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

@section('page-style')
<style>
    :root {
        --primary-color: #003D4C;
        --secondary-color: #0094B3;
        --border-color: #e2e8f0;
        --bg-hover: rgba(0, 148, 179, 0.05);
    }

    .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.5rem;
        margin-top: 2rem;
    }

    .pagination .page-item .page-link {
        border-radius: 8px;
        color: var(--primary-color);
        padding: 0.5rem 1rem;
        transition: all 0.3s ease;
    }

    .pagination .page-item.active .page-link {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    .pagination .page-item .page-link:hover {
        background-color: var(--bg-hover);
        border-color: var(--border-color);
        color: var(--primary-color);
    }

    .pagination .page-item.disabled .page-link {
        color: #6c757d;
        pointer-events: none;
        background-color: #fff;
        border-color: #dee2e6;
    }

    .card {
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.05);
        border: none;
    }

    .card-header {
        background: #fff;
        border-bottom: 1px solid var(--border-color);
        padding: 1.5rem;
    }

    .table {
        margin-bottom: 0;
    }

    .table th {
        font-weight: 600;
        color: var(--primary-color);
        background: #f8fafc;
        border-bottom: 2px solid var(--border-color);
    }

    .table td {
        vertical-align: middle;
        color: #4a5568;
    }

    .btn-primary {
        background: var(--primary-color);
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background: var(--secondary-color);
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .badge {
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        font-weight: 500;
    }

    .btn-sm {
        padding: 0.4rem 0.8rem;
        font-size: 0.875rem;
    }

    .individual-info {
        font-size: 0.875rem;
        color: #4a5568;
    }
</style>
@endsection

@section('content')
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قائمة طلبات تأمين الأفراد</h5>
        <a href="{{ route('individual-orders.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            انشاء طلب جديد
        </a>
    </div>
    <ul class="nav nav-tabs mt-3" id="ordersTab" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="system-orders-tab" data-bs-toggle="tab" data-bs-target="#system-orders" type="button" role="tab" aria-controls="system-orders" aria-selected="true">طلبات النظام</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="external-orders-tab" data-bs-toggle="tab" data-bs-target="#external-orders" type="button" role="tab" aria-controls="external-orders" aria-selected="false">طلبات من قاعدة بيانات خارجية</button>
        </li>
    </ul>
    <div class="table-responsive">
        <table class="table">
            <thead>
                <tr>
                    <th>رقم الطلب</th>
                    <th>نوع الطلب</th>
                    <th>مجموع القسط</th>
                    <th>الحالة</th>
                    <th>اجراءات</th>
                </tr>
            </thead>
            <tbody>
                @foreach($orders as $order)
                <tr>
                    <td>{{ $order->id }}</td>
                    <td>{{ getInsuTypeById($order->type_id) }}</td>
                    <td>{{ $order->total_price }}</td>
                    <td>
                        @if($order->status == 0 && auth()->user()->role === 'admin')
                            <button class="btn btn-success btn-sm change-status" data-id="{{ $order->id }}" data-status="1">قبول</button>
                            <button class="btn btn-danger btn-sm change-status" data-id="{{ $order->id }}" data-status="2">رفض</button>
                        @else
                            @if($order->status == 1)
                                <span class="badge bg-success">مقبول</span>
                            @elseif($order->status == 2)
                                <span class="badge bg-danger">مرفوض</span>
                            @elseif($order->status == 0)
                                <span class="badge bg-secondary">في الإنتظار</span>
                            @endif
                        @endif
                    </td>
                    <td>
                        <div class="d-flex gap-2">
                            <a href="{{ route('individual-orders.edit', $order->id) }}" class="btn btn-sm btn-primary">
                                <i class="fas fa-eye me-1"></i>
                                عرض التفاصيل
                            </a>
                            <button class="btn btn-sm btn-info btn-renew" data-id="{{ $order->id }}">
                                <i class="fas fa-sync-alt me-1"></i>
                                تجديد
                            </button>
                        </div>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>

        <!-- Pagination -->
        <div class="d-flex justify-content-center mt-4 mb-4">
            {{ $orders->links('pagination::bootstrap-4') }}
        </div>
         <div class="tab-pane fade" id="external-orders" role="tabpanel" aria-labelledby="external-orders-tab">
            <div class="mt-4 mb-3">
                <div class="input-group mb-2" style="max-width:400px;">
                    <input id="external-search-input" class="form-control" placeholder="ادخل رقم البوليصة ">
                    <button class="btn btn-secondary" id="external-search-btn" type="button">بحث</button>
                </div>
            </div>
            <div id="external-orders-result"></div>
        </div>
    </div>
</div>
<input type="hidden" id="customer-id" value="{{ auth()->user()->identity ?? auth()->id() }}">

@endsection

@section('scripts')
<script>
$(document).ready(function() {
    $('.change-status').click(function() {
        const id = $(this).data('id');
        const status = $(this).data('status');

        // Show popup for note
        Swal.fire({
            title: 'أدخل الملاحظة',
            input: 'textarea',
            inputLabel: 'ملاحظة',
            inputPlaceholder: 'اكتب ملاحظتك هنا...',
            showCancelButton: true,
            confirmButtonText: 'إرسال',
            cancelButtonText: 'إلغاء',
            inputValidator: (value) => {
                if (!value) return 'الرجاء إدخال ملاحظة';
            }
        }).then((result) => {
            if (result.isConfirmed) {
                const note = result.value;

                $.ajax({
                    url: "{{ route('insurance-orders.updateStatus', ':id') }}".replace(
                        ':id', id),
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        status: status,
                        note: note
                    },
                    success: function(response) {
                        Swal.fire('تم التحديث!', '', 'success').then(() => {
                            location.reload();
                        });
                    },
                    error: function() {
                        Swal.fire('حدث خطأ!', 'يرجى المحاولة مرة أخرى', 'error');
                    }
                });
            }
        });
    });
    // Renewal handler
    $('.btn-renew').click(function(e) {
        e.preventDefault();
        const id = $(this).data('id');
        
        Swal.fire({
            title: 'تأكيد التجديد',
            text: 'هل تريد تجديد هذا الطلب؟',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#003D4C',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم',
            cancelButtonText: 'لا'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `{{ route('insurance-orders.renew', ':id') }}`.replace(':id', id),
                    method: 'GET',
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'تم!',
                                text: response.message,
                                icon: 'success',
                                confirmButtonColor: '#003D4C'
                            }).then(() => {
                                location.reload();
                            });
                        }
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON;
                        Swal.fire({
                            title: 'خطأ!',
                            text: response?.message || 'حدث خطأ أثناء تجديد الطلب',
                            icon: 'error',
                            confirmButtonColor: '#003D4C'
                        });
                    }
                });
            }
        });
    });
     let externalOrdersData = [];
    function renderExternalOrdersTable(data) {
        if (data.length > 0) {
            let table = `<table class='table mt-3'><thead><tr>`;
            table += `<th>ID</th>`;
            table += `<th>رقم الشصي</th>`;
            table += `<th>رقم المركبة</th>`;
            table += `<th>تاريخ انتهاء الترخيص</th>`;
            table += `<th>الفرع</th>`;
            table += `<th>عرض</th>`;
            table += `</tr></thead><tbody>`;
            data.forEach(function(row, idx) {
                table += '<tr>';
                table += `<td>${row.ID ?? ''}</td>`;
                table += `<td>${row.ICLM_CHASI_NO ?? ''}</td>`;
                table += `<td>${row.ICLM_PLATE_NO ?? ''}</td>`;
                table += `<td>${row.ICLM_INS_ED_DT ?? ''}</td>`;
                table += `<td>${row.ICLM_BRANCH ?? ''}</td>`;
                table += `<td><a class='btn btn-sm btn-primary' href='/external-edit/${row.ICLM_CHASI_NO}'>عرض</a></td>`;
                table += '</tr>';
            });
            table += '</tbody></table>';
            $('#external-orders-result').html(table);
        } else {
            $('#external-orders-result').html('<div class="alert alert-warning">لم يتم العثور على نتائج.</div>');
        }
    }
    function fetchExternalOrdersFromApi() {
        const customerId = $('#customer-id').val();
        $('#external-orders-result').html('جاري التحميل...');
        $.ajax({
            url: 'http://82.213.0.2:8000/renewal-policy?cust_id=' + encodeURIComponent(customerId),
            method: 'GET',
            success: function(res) {
                if (res && Array.isArray(res) && res.length > 0) {
                    externalOrdersData = res;
                    renderExternalOrdersTable(res);
                } else if (res && res.data && Array.isArray(res.data) && res.data.length > 0) {
                    externalOrdersData = res.data;
                    renderExternalOrdersTable(res.data);
                } else {
                    externalOrdersData = [];
                    $('#external-orders-result').html('<div class="alert alert-warning">لم يتم العثور على نتائج.</div>');
                }
            },
            error: function() {
                $('#external-orders-result').html('<div class="alert alert-danger">حدث خطأ أثناء التحميل.</div>');
            }
        });
    }
    // Fetch on tab show
    $('button#external-orders-tab').on('shown.bs.tab', function() {
        fetchExternalOrdersFromApi();
    });
    // Also fetch immediately if already active
    if ($('button#external-orders-tab').hasClass('active')) {
        fetchExternalOrdersFromApi();
    }
    // Search handler (client-side)
    $('#external-search-btn').on('click', function() {
        const q = $('#external-search-input').val().toLowerCase();
        if (!q) {
            renderExternalOrdersTable(externalOrdersData);
            return;
        }
        const filtered = externalOrdersData.filter(row => {
            return Object.values(row).some(val => (val+'' || '').toLowerCase().includes(q));
        });
        renderExternalOrdersTable(filtered);
    });

    $(document).on('click', '.view-chasi-btn', function() {
        const chasi = $(this).data('chasi');
        if (!chasi) return;
        window.open('{{route('individual-orders.show', ':id')}}'.replace(':id', chasi), '_blank');
    });
});
</script>
@endsection