@extends('layouts.wordpressLayout')

@section('title', 'عرض التأمين الشخصي')

@section('content')
<div class="row">
    <h5 class="mb-0">تفاصيل التأمين الشخصي</h5>

    <div class="row mb-4">
        <div class="col-xxl">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h5 class="card-title mb-4 text-center">بيانات البرنامج</h5>

                    {{-- نوع البرنامج --}}
                    <div class="row mb-3">
                        <label class="col-sm-3 col-form-label">البرنامج</label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" readonly 
                                value="{{ collect($programs)->firstWhere('program', $order->PersonalInsuranceDetails->p_pacc_program)['prog_desc'] ?? '' }}">
                        </div>
                    </div>

                    {{-- تواريخ --}}
                    <div class="row mb-3">
                        <label class="col-sm-3 col-form-label">تاريخ البداية</label>
                        <div class="col-sm-9">
                            <div class="input-group">
                                <span class="input-group-text"><i class='bx bx-calendar'></i></span>
                                <input type="text" class="form-control" readonly
                                    value="{{$order->PersonalInsuranceDetails->p_ins_start_dt}}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <label class="col-sm-3 col-form-label">تاريخ النهاية</label>
                        <div class="col-sm-9">
                            <div class="input-group">
                                <span class="input-group-text"><i class='bx bx-calendar'></i></span>
                                <input type="text" class="form-control" readonly
                                    value="{{$order->PersonalInsuranceDetails->p_ins_end_dt}}">
                            </div>
                        </div>
                    </div>

                    {{-- قيمة التأمين --}}
                    <div class="row mb-3">
                        <label class="col-sm-3 col-form-label">قيمة التأمين</label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" readonly
                                value="{{$order->PersonalInsuranceDetails->p_si}}">
                        </div>
                    </div>

                    {{-- Additional Details --}}
                    <div class="row mb-3">
                        <label class="col-sm-3 col-form-label">حالة الطلب</label>
                        <div class="col-sm-9">
                            <div class="badge bg-{{ $order->status == 1 ? 'success' : ($order->status == 2 ? 'danger' : 'secondary') }} fs-6">
                                {{ $order->status == 1 ? 'مقبول' : ($order->status == 2 ? 'مرفوض' : 'في الإنتظار') }}
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label class="col-sm-3 col-form-label">تاريخ الطلب</label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" readonly
                                value="{{$order->created_at->format('Y-m-d')}}">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label class="col-sm-3 col-form-label">إجمالي القسط</label>
                        <div class="col-sm-9">
                            <input type="text" class="form-control" readonly
                                value="{{$order->total_price}}">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Questions Section --}}
    <div class="row mb-4">
        <div class="col-xxl">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h5 class="card-title mb-4 text-center">الأسئلة والإجابات</h5>
                    
                    @foreach($order->PersonalInsuranceDetails->qustionsOreder as $answer)
                    <div class="mb-4">
                        <h6 class="text-primary mb-2">{{ $answer->question_text }}</h6>
                        <div class="bg-light p-3 rounded">
                            <p class="mb-0">{{ $answer->answer }}</p>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    {{-- Persons Section --}}
    <div class="row mb-4">
        <div class="col-xxl">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h5 class="card-title mb-4 text-center">بيانات الأشخاص المؤمن عليهم</h5>
                    
                    @foreach($order->PersonalInsuranceDetails->personsOreder as $person)
                    <div class="card mb-3">
                        <div class="card-body">
                            <h6 class="card-subtitle mb-3">الشخص {{ $loop->iteration }}</h6>
                            
                            <div class="row mb-2">
                                <label class="col-sm-3 col-form-label">الاسم</label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control" readonly value="{{ $person->cust_aname }}">
                                </div>
                            </div>
                            
                            <div class="row mb-2">
                                <label class="col-sm-3 col-form-label">رقم الهوية</label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control" readonly value="{{ $person->cust_id }}">
                                </div>
                            </div>
                            
                            <div class="row mb-2">
                                <label class="col-sm-3 col-form-label">رقم الجوال</label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control" readonly value="{{ $person->cust_mob }}">
                                </div>
                            </div>
                            
                            <div class="row mb-2">
                                <label class="col-sm-3 col-form-label">تاريخ الميلاد</label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control" readonly value="{{ $person->cust_dob }}">
                                </div>
                            </div>
                            
                            <div class="row mb-2">
                                <label class="col-sm-3 col-form-label">العنوان</label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control" readonly value="{{ $person->cust_address }}">
                                </div>
                            </div>
                            
                            <div class="row mb-2">
                                <label class="col-sm-3 col-form-label">البريد الإلكتروني</label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control" readonly value="{{ $person->cust_email }}">
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12 text-center">
            <a href="{{ route('individual-orders.index') }}" class="btn btn-primary">
                <i class='bx bx-arrow-back me-1'></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>
@endsection