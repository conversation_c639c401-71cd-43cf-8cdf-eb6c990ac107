@extends('layouts.wordpressLayout')

@section('title', 'حاسبة قسط التأمين الشخصي')

{{-- jQuery CDN --}}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
{{-- Boxicons CSS --}}
<link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
{{-- Modern Style CSS --}}
<link href="{{ asset('assets/css/modern-style.css') }}\" rel="stylesheet">

@section('page-style')
<link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
<style>
    .insurance-form-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    .form-section {
        background: #fff;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.05);
        padding: 2rem;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }

    .form-section:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .section-title {
        color: #003D4C;
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #edf2f7;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title i {
        font-size: 1.5rem;
        color: #0094B3;
    }

    .form-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .form-header h4 {
        color: #003D4C;
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .form-label {
        font-weight: 500;
        color: #4a5568;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border-radius: 0px;
        border: 1.5px solid #e2e8f0;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #0094B3;
        box-shadow: 0 0 0 3px rgba(0, 148, 179, 0.1);
    }

    .input-group-text {
        background: #f8fafc;
        border: 1.5px solid #e2e8f0;
        border-radius: 0px;
        padding: 0.75rem;
    }

    .input-group-text i {
        color: #0094B3;
        font-size: 1.2rem;
    }

    .input-group > .form-control {
        border-start-start-radius: 0;
        border-end-start-radius: 0;
    }

    .input-group > .input-group-text {
        border-start-end-radius: 0;
        border-end-end-radius: 0;
    }

    .btn-primary {
        background: #003D4C;
        border: none;
        padding: 0.8rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background: #0094B3;
        transform: translateY(-2px);
    }

    .price-display {
        font-size: 2.5rem;
        font-weight: 700;
        color: #003D4C;
        margin: 2rem 0;
    }

    .customer-card {
        background: #fff;
        border-radius: 15px;
        border: 1.5px solid #e2e8f0;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .customer-card:hover {
        border-color: #0094B3;
        transform: translateY(-2px);
    }

    .customer-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #edf2f7;
    }

    .customer-title {
        color: #003D4C;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-remove-customer {
        background: #fee2e2;
        color: #dc2626;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }

    .btn-remove-customer:hover {
        background: #fecaca;
        transform: translateY(-1px);
    }

    .error-feedback {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .btn-group {
        gap: 0.5rem;
    }

    .btn-check + .btn-outline-primary {
        color: #003D4C;
        border-color: #003D4C;
        padding: 0.5rem 2rem;
    }

    .btn-check:checked + .btn-outline-primary {
        background-color: #003D4C;
        border-color: #003D4C;
        color: white;
    }

    .submit-section {
        text-align: center;
        margin-top: 3rem;
    }

    .btn-submit {
        background: #003D4C;
        color: white;
        border: none;
        padding: 1rem 3rem;
        border-radius: 10px;
        font-weight: 600;
        font-size: 1.1rem;
        min-width: 200px;
        transition: all 0.3s ease;
    }

    .btn-submit:hover {
        background: #0094B3;
        transform: translateY(-2px);
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255,255,255,0.9);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #003D4C;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* RTL Specific Styles */
    .rtl {
        direction: rtl;
        text-align: right;
    }

    .rtl .input-group > .form-control {
        border-start-end-radius: 0;
        border-end-end-radius: 0;
        border-start-start-radius: 0px;
        border-end-start-radius: 0px;
    }

    .rtl .input-group > .input-group-text {
        border-start-start-radius: 0px;
        border-end-start-radius: 0px;
        border-start-end-radius: 0;
        border-end-end-radius: 0;
    }

    .rtl .me-2 {
        margin-left: 0.5rem !important;
        margin-right: 0 !important;
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .insurance-form-container {
            padding: 1rem;
        }

        .form-section {
            padding: 1.5rem;
        }

        .form-header h4 {
            font-size: 1.5rem;
        }

        .price-display {
            font-size: 2rem;
        }

        .btn-submit {
            width: 100%;
        }
    }
</style>
@endsection

@section('content')
<div class="insurance-form-container rtl">
    <div class="form-header">
        <h4>حاسبة قسط التأمين الشخصي</h4>
        <p class="text-muted">
            توفر شركة التكافل أداة ذكية وسهلة الاستخدام لحساب قسط تأمينك الشخصي بدقة وسرعة.
            تتيح لك الحاسبة إدخال بياناتك الأساسية مثل العمر، المهنة، نوع البرنامج التأميني، والمبلغ الذي ترغب بتغطيته،
            لتحصل فورًا على تقدير تقريبي لقيمة القسط التأميني.
        </p>
    </div>

    <form id="individual-calc-form" action="{{ route('insurance-orders.store') }}" method="POST">
        @csrf
        <input type="hidden" name="type_id" value="4">

        {{-- بيانات البرنامج --}}
        <div class="form-section">
            <div class="section-title">
                <i class='bx bx-package'></i>
                <span>بيانات البرنامج</span>
            </div>

            <div class="row">
                <div class="col-md-12 mb-4">
                    <label class="form-label">البرنامج</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class='bx bx-list-check'></i>
                        </span>
                        <select class="form-select" id="p_pacc_program" name="details[p_pacc_program]">
                            <option value="select">اختر البرنامج</option>
                            @foreach($programs as $item)
                            <option value="{{$item['program']}}" 
                                    data-from="{{$item['from_si']}}"
                                    data-to="{{$item['to_si']}}" 
                                    data-min-prem="{{$item['min_prem']}}"
                                    data-perc="{{$item['perc']}}">
                                {{ $item['program'] }} - {{$item['prog_desc']}} - {{$item['from_si']}} - {{$item['to_si']}}
                            </option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-4">
                    <label class="form-label">تاريخ البداية</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class='bx bx-calendar'></i>
                        </span>
                        <input type="date" class="form-control" id="p_ins_start_dt" name="details[p_ins_start_dt]">
                    </div>
                </div>

                <div class="col-md-6 mb-4">
                    <label class="form-label">تاريخ النهاية</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class='bx bx-calendar-x'></i>
                        </span>
                        <input type="date" class="form-control" id="p_ins_end_dt" name="details[p_ins_end_dt]">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12 mb-4">
                    <label class="form-label">قيمة التأمين</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class='bx bx-money'></i>
                        </span>
                        <input type="number" class="form-control" id="p_si" name="details[p_si]">
                        <span class="input-group-text">شيقل</span>
                    </div>
                    <div class="error-feedback mt-2" id="si_message"></div>
                </div>
            </div>

            <div class="text-end mt-4">
                <button type="button" class="btn btn-primary" id="calc-btn">
                    <i class='bx bx-calculator me-2'></i>
                    احسب القسط
                </button>
            </div>
        </div>

        {{-- نتائج حساب الاقساط --}}
        <div class="form-section">
            <div class="section-title">
                <i class='bx bx-receipt'></i>
                <span>نتائج حساب الأقساط</span>
            </div>
            <div class="text-center">
                <div class="price-display">
                    <span id="total_cost">0</span> شيقل
                </div>
                <input type="hidden" name="details[p_prem]">
                <input type="hidden" name="details[p_fees]">
                <input type="hidden" name="details[p_min_prem]">
                <input type="hidden" name="details[p_perc]">
            </div>
        </div>

        {{-- بيانات العميل --}}
        <div class="form-section">
            <div class="section-title">
                <i class='bx bx-user'></i>
                <span>بيانات العميل</span>
            </div>

            <div class="row">
                @php
                $userFields = [
                    ['label' => 'اسم المؤمن له', 'id' => 'customer_name', 'name' => 'insured_full_name', 'value' => auth()?->user()?->fullname, 'icon' => 'bx-user'],
                    ['label' => 'رقم الهوية', 'id' => 'customer_id', 'name' => 'insured_identity', 'value' => auth()?->user()?->identity, 'icon' => 'bx-id-card'],
                    ['label' => 'تاريخ الميلاد', 'id' => 'customer_dob', 'name' => 'insured_date_of_birth', 'value' => auth()?->user()?->date_of_birth, 'icon' => 'bx-calendar'],
                    ['label' => 'البريد الإلكتروني', 'id' => 'customer_email', 'name' => 'insured_email', 'value' => auth()?->user()?->email, 'icon' => 'bx-envelope'],
                    ['label' => 'رقم الهاتف', 'id' => 'customer_phone', 'name' => 'insured_phone', 'value' => auth()?->user()?->mobile, 'icon' => 'bx-phone'],
                ];
                @endphp

                @foreach($userFields as $field)
                <div class="col-md-6 mb-4">
                    <label class="form-label" for="{{ $field['id'] }}">{{ $field['label'] }}</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class='bx {{ $field['icon'] }}'></i>
                        </span>
                        <input type="text" name="details[{{ $field['name'] }}]" id="{{ $field['id'] }}"
                               class="form-control" value="{{ $field['value'] }}" required>
                    </div>
                </div>
                @endforeach
            </div>
        </div>

        {{-- الأسئلة --}}
        <div class="form-section">
            <div class="section-title">
                <i class='bx bx-question-mark'></i>
                <span>الأسئلة</span>
            </div>
            @foreach($questions as $q)
              <div class="mb-3">
                <label class="form-label d-block">{{ $q['question_adesc'] }}</label>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" name="questions[{{ $q['question_id'] }}]" id="{{ $q['question_id'] }}_yes" value="yes">
                  <label class="form-check-label" for="{{ $q['question_id'] }}_yes">نعم</label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" name="questions[{{ $q['question_id'] }}]" id="{{ $q['question_id'] }}_no" value="no">
                  <label class="form-check-label" for="{{ $q['question_id'] }}_no">لا</label>
                </div>
              </div>
            @endforeach

           
        </div>

        {{-- إضافة مؤمنين --}}
        <div class="form-section">
            <div class="section-title">
                <i class='bx bx-group'></i>
                <span>المؤمن عليهم الإضافيين</span>
            </div>

            <div id="customersContainer" class="row"></div>

            <button type="button" class="btn btn-primary mt-3" onclick="addCustomer()">
                <i class='bx bx-user-plus me-2'></i>
                إضافة شخص
            </button>
        </div>

        {{-- زر الإرسال --}}
        <div class="submit-section">
            <button type="submit" class="btn btn-submit">
                <i class='bx bx-check-circle me-2'></i>
                إرسال الطلب
            </button>
            <p class="text-muted mt-3">
                بالضغط على زر الإرسال، أنت توافق على الشروط والأحكام
            </p>
        </div>
    </form>
</div>

<div class="loading-overlay">
    <div class="loading-spinner"></div>
</div>
@endsection

@section('page-script')
<script>
const programSelect = document.getElementById('p_pacc_program');
const siInput = document.getElementById('p_si');
const premiumInput = document.getElementById('total_cost');
const messageDiv = document.getElementById('si_message');
const calcBtn = document.getElementById('calc-btn');
const loadingOverlay = document.querySelector('.loading-overlay');

function showMessage(text) {
    messageDiv.innerText = text;
    messageDiv.style.display = 'block';
}

function calculatePremium(value, minPrem, perc) {
    loadingOverlay.style.display = 'flex';
    
    const calculated = value * parseFloat(perc);
    const si_p = calculated < parseFloat(minPrem) ? parseFloat(minPrem) : calculated.toFixed(2);

    const formData = {
        p_si: si_p,
        p_ins_start_dt: $('#p_ins_start_dt').val(),
        p_ins_end_dt: $('#p_ins_end_dt').val(),
        access_token: $('meta[name="csrf-token"]').attr('content')
    };

    $.ajax({
        url: 'http://**********:8000/pacc-prem',
        type: 'GET',
        data: formData,
        success: function(response) {
            console.log(response);
            premiumInput.textContent = response.prem;
            $('input[name="details[p_prem]"]').val(response.prem);
            $('input[name="details[p_min_prem]"]').val(response.min_prem);
            $('input[name="details[p_perc]"]').val(response.perc);
            $('input[name="p_fees"]').val(response.fees);
            loadingOverlay.style.display = 'none';
        },
        error: function(xhr, status, error) {
            console.log(xhr.responseText);
            $('#responseMessage').html('<p class="error-feedback">خطأ: ' + error + '</p>');
            loadingOverlay.style.display = 'none';
        }
    });
}

calcBtn.addEventListener('click', function() {
    const selected = programSelect.options[programSelect.selectedIndex];

    if (!selected || programSelect.value === 'select') {
        showMessage('يرجى اختيار البرنامج أولاً');
        premiumInput.textContent = '0';
        return;
    }

    const fromSi = parseFloat(selected.dataset.from);
    const toSi = parseFloat(selected.dataset.to);
    const minPrem = selected.dataset.minPrem;
    const perc = selected.dataset.perc;

    let value = parseFloat(siInput.value);

    if (!value) {
        siInput.value = toSi;
        value = toSi;
    }

    if (value < fromSi) {
        premiumInput.textContent = '0';
        showMessage("مبلغ التامين اقل من البرامج المدرجة");
        return;
    }

    if (value > toSi) {
        premiumInput.textContent = '0';
        showMessage("مبلغ التامين اعلى من البرامج المدرجة");
        return;
    }

    calculatePremium(value, minPrem, perc);
});

let customerCount = 0;

function addCustomer() {
    const customerHTML = `
        <div class="col-12 mb-4" id="customer-${customerCount}">
            <div class="customer-card">
                <div class="customer-header">
                    <h6 class="customer-title">
                        <i class='bx bx-user'></i>
                        بيانات الشخص ${customerCount + 1}
                    </h6>
                    <button type="button" class="btn-remove-customer" onclick="removeCustomer(${customerCount})">
                        <i class='bx bx-trash'></i>
                        حذف
                    </button>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">الاسم</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class='bx bx-user'></i>
                            </span>
                            <input type="text" class="form-control" name="customers[${customerCount}][name]" required>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label">رقم الهوية</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class='bx bx-id-card'></i>
                            </span>
                            <input type="text" class="form-control" name="customers[${customerCount}][identity]" required>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">تاريخ الميلاد</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class='bx bx-calendar'></i>
                            </span>
                            <input type="date" class="form-control" name="customers[${customerCount}][dob]" required>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label">رقم الهاتف</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class='bx bx-phone'></i>
                            </span>
                            <input type="tel" class="form-control" name="customers[${customerCount}][phone]" required>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('customersContainer').insertAdjacentHTML('beforeend', customerHTML);
    customerCount++;
}

function removeCustomer(index) {
    const el = document.getElementById(`customer-${index}`);
    if (el) {
        el.style.transform = 'translateY(10px)';
        el.style.opacity = '0';
        setTimeout(() => {
            el.remove();
        }, 300);
    }
}

// Form validation
document.getElementById('individual-calc-form').addEventListener('submit', function(e) {
    if (!validateForm()) {
        e.preventDefault();
        alert('يرجى التأكد من إدخال جميع البيانات المطلوبة');
    }
});

function validateForm() {
    const requiredFields = document.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value) {
            isValid = false;
            field.classList.add('is-invalid');
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}
</script>
@endsection