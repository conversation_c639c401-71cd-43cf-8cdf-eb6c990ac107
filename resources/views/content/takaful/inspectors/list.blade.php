@extends('layouts/contentNavbarLayout')

@section('title', 'Tables - Basic Tables')

@section('content')
<div class="card">
  <h5 class="card-header"> أحدث المعاينات </h5>
  <div class="table-responsive text-nowrap">
    <table class="table">
      <thead class="table-light">
        <tr>
          <th>رقم المعاينة</th>
          <th>اسم المعاين</th>
          <th>رقم الشصى</th>
          <th>الحالة</th>
          <th>اجراءات</th>
        </tr>
      </thead>
      <tbody class="table-border-bottom-0">
        @foreach($groupedData as $index => $row)
        <tr>
            <td> <span>{{ $index }}</span></td>
            <td>{{ $row[0]['mph_user'] }}</td>
            <td>{{ $row[0]['mph_chasi'] }}</td>
            <td><span class="badge bg-label-primary me-1">Active</span></td>
            <td>
                <button class="btn btn-sm btn-primary" data-bs-toggle="collapse" data-bs-target="#collapseRow{{ $index }}" aria-expanded="false">
                    <i class="bx bx-chevron-down"></i>
                </button>
            </td>
        </tr>

        <tr id="collapseRow{{ $index }}" class="collapse">
            <td colspan="5">
                <div class="p-3 border rounded bg-light">
                    <strong>مجموعة الصور:</strong>
                    <table class="table table-bordered mt-2">
                        <thead>
                            <tr>
                                <th>الترتيب</th>
                                <th>النوع</th>
                                <th>الصورة</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($row as $item)
                                @if(isset($item['photos']))
                                <tr>
                                    <td>{{ $item['photos']['mph_photo_seq'] ?? 'N/A' }}</td>
                                    <td>{{ $item['photos']['mph_photo_type'] ?? 'N/A' }}</td>
                                    <td>
                                        @if(!empty($item['photos']['mph_photo_file']))
                                            <img src="{{ asset($item['photos']['mph_photo_file']) }}" width="100">
                                        @else
                                            No Image Available
                                        @endif
                                    </td>
                                </tr>
                                @endif
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </td>
        </tr>
        @endforeach
      </tbody>
    </table>
  </div>

  <!-- Pagination Links -->
  <div class="d-flex justify-content-center mt-3">
    {{ $inspectors->links('vendor.pagination.bootstrap-5') }}
  </div>

</div>


@endsection