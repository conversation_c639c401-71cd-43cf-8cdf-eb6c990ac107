@extends('layouts/wordpressLayout')

@section('title', 'تفاصيل طلب التامين')
<!-- Add jQuery from CDN (make sure this is ABOVE your script) -->

@section('content')
<!-- Basic Layout & Basic with Icons -->
<div class="row">
 
  <form id="car-calc-form">
    @csrf
    <div class="col-xxl">
      <div class="card mb-6">
        <div class="card-header d-flex align-items-center justify-content-between">
        </div>
        <div class="card-body">

            <div class="row mb-6">
              <label class="col-sm-3 col-form-label" for="location">المنطقة / التعرفة</label>
              <div class="col-sm-9">
                <select class="form-select" name="details[policy_location	]" id="location" aria-label="Default select example">
                  <option selected value="فلسطينية">فلسطينية</option>
                  <option value="القدس">القدس</option>
                </select>
              </div>
            </div>
            <!-- ✅ القسم الأول (يظهر فقط عند اختيار "فلسطينية") -->
            <div class="row mb-6" id="insurance-unified">
              <label class="col-sm-3 col-form-label">نوع التأمين</label>
              <div class="col-sm-9">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" name="insurance_type" value="102" id="insurance0">
                  <label class="form-check-label" for="insurance0">
                    تأمين الزامي و طرف ثالث (الموحد)
                  </label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" name="insurance_type" value="شامل" id="insurance1">
                  <label class="form-check-label" for="insurance1">
                      شامل
                  </label>
                </div>
              </div>
            </div>
            <div class="row mb-6 d-none" id="insurance-detailed">
              <label class="col-sm-3 col-form-label">نوع التأمين</label>
              <div class="col-sm-9">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" name="insurance_type" value="تأمين الزامي" id="insurance2">
                  <label class="form-check-label" for="insurance2">تأمين الزامي</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" name="insurance_type" value="شامل" id="insurance3">
                  <label class="form-check-label" for="insurance3">شامل</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" name="insurance_type" value="طرف ثالث" id="insurance4">
                  <label class="form-check-label" for="insurance4">طرف ثالث</label>
                </div>
              </div>
            </div>
            <div class="row mb-6">
              <label class="col-sm-3 col-form-label" for="used-type">نوع الترخيص</label>
              <div class="col-sm-9">
              <select name="details[policy_use]" id="item_id" class="form-select" aria-label="Default select example">
             
                    <option value=""></option>
              
              </select>
              </div>
            </div>
            <div class="row mb-6">
              <label class="col-sm-3 col-form-label" for="used-type-spc">الاستعمال الخاص</label>
              <div class="col-sm-9">
                  <select class="form-select" name="details[policy_spical_use]" id="used-type-spc" aria-label="Default select example">
                      <option selected value=""></option>
                  </select>
              </div>
            </div>
            <div class="row mb-6" id="engine-capacity">
              <label class="col-sm-3 col-form-label" for="engine-capacity">مواصفات المركبة <br> (سعة المحرك)</label>
              <div class="col-sm-9">
                <select class="form-select" name="details[policy_cc]" id="engine-capacity-val" aria-label="Default select example ">
                  <option value="1200">1200</option>
                  <option value="1400">1400</option>
                  <option value="1600">1600</option>
                  <option value="1800">1800</option>
                  <option value="2000">2000</option>
                </select>
              </div>
            </div>
            <div class="row mb-6" id="weight-field">
              <label class="col-sm-3 col-form-label" for="waight"> الوزرن بالطن</label>
              <div class="col-sm-9">
                <input type="text" id="waight" name=" details[policy_load]" class="form-control phone-mask" aria-describedby="basic-default-phone" />
              </div>
            </div>
            <div class="row mb-6" id="passenger-number">
              <label class="col-sm-3 col-form-label" for="number-passenger"> عدد الركاب</label>
              <div class="col-sm-9">
                <input type="text" name="details[policy_seats]" id="number-passenger" class="form-control phone-mask" aria-describedby="basic-default-phone" />
              </div>
            </div>
            <div class="row mb-6">
              <label class="col-sm-3 col-form-label" for="insurance-type">نوع فترة التأمين</label>
              <div class="col-sm-9">
                <select class="form-select" id="insurance-type" aria-label="Default select example">
                  <option selected disabled>اختر</option>
                  <option value="سنة">سنة</option>
                  <option value="شهر">شهر</option>
                  <option value="يوم">يوم</option>
                </select>
              </div>
            </div>
            <div class="row mb-6">
              <label class="col-sm-3 col-form-label" for="insurance-period">فترة التأمين المطلوبة</label>
              <div class="col-sm-9">
                <input type="number" id="insurance-period" class="form-control" />
              </div>
            </div>
            <div class="row mb-6">
              <label class="col-sm-3 col-form-label" for="basic-default-phone">
                مبلغ التأمين<br><small>(سعر المركبة - شيكل)</small>
              </label>
              <div class="col-sm-9">
                <input type="text" id="price-car" name=" details[policy_inurance_vechile_price]" class="form-control phone-mask"  aria-describedby="basic-default-phone" value="{{ $order?->vehicleInsuranceDetails?->policy_inurance_vechile_price }}" />
              </div>
            </div>
            <div class="row mb-6">
              <label class="col-sm-3 col-form-label" for="basic-default-phone"> سنة الصنع</label>
              <div class="col-sm-9">
                <input type="text" id="date-prod" name=" details[policy_prod_year]" class="form-control phone-mask"  aria-describedby="basic-default-phone" value="{{$order?->vehicleInsuranceDetails?->policy_prod_year}}" />
              </div>
            </div>
          <!-- </form> -->
        </div>
      </div>
    </div>
    <!--  قسم حسبة التامين-->
    <div class="col-xxl">
      <div class="card shadow-sm">
        <div class="card-body text-center">
          <h5 class="card-title mb-4">نتائج حساب الاقساط</h5>
          <div class="mt-4">
            <label class="col-sm-3 col-form-label">قسط التأمين الإلزامي و الطرف الثالث</label>
            <input type="text" readonly class="form-control text-center border-primary fw-bold" id="insuranceAmount" value="">
          </div>
          <div class="mt-4" >
            <label class="col-sm-3 col-form-label">قسط التأمين (الشامل)</label>
            <input type="text" id='compValue' readonly class="form-control text-center border-primary fw-bold" value="">
          </div>
          <div class="mt-4" id="fullcost"  style="display: none;">
            <label class="form-label fw-bold">تكلفة التأمين</label>
            <div class="bg-primary text-white py-2 rounded fw-bold" id='full' name="total_cost">
            </div>
          </div>
        </div>
      </div>
    </div>

<!--user info section -->
<div class="col-xxl">
      <div class="card shadow-sm">
        <div class="card-body text-center">
          <h5 class="card-title mb-4">بيانات العميل </h5>
          <div class="row mb-6">
            <label class="col-sm-3 col-form-label" for="name">اسم المؤمن له </label>
            <div class="col-sm-9">
              <input  type="text" name="details[insured_full_name]" id="customer_name" class="form-control phone-mask"  aria-describedby="basic-default-phone" value= "{{auth()->user()->fullname}}" />
            </div>
          </div>
          <div class="row mb-6">
            <label class="col-sm-3 col-form-label" for="id">رقم هوية المؤمن له </label>
            <div class="col-sm-9">
              <input  type="text" name="details[insured_identity]" id="customer_id" class="form-control phone-mask"  aria-describedby="basic-default-phone" value="{{auth()->user()->identity}}" />
            </div>
          </div>
          <div class="row mb-6">
            <label class="col-sm-3 col-form-label" for="dob">تاريخ ميلاد المؤمن له </label>
            <div class="col-sm-9">
              <input  type="text" name="details[insured_date_of_birth]" id="customer_dob" class="form-control phone-mask"  aria-describedby="basic-default-phone" value="{{auth()->user()->date_of_birth}}" />
            </div>
          </div>
          <div class="row mb-6">
            <label class="col-sm-3 col-form-label" for="email">البريد الالكتروني المؤمن له</label>
            <div class="col-sm-9">
              <input  type="text" name="details[insured_emai]" id="customer_email" class="form-control phone-mask"  aria-describedby="basic-default-phone" value="{{auth()->user()->email}}" />
            </div>
          </div>
          <div class="row mb-6">
            <label class="col-sm-3 col-form-label" for="phone">رقم هاتف المؤمن له </label>
            <div class="col-sm-9">
              <input  type="text" name="details[insured_phone]" id="customer_phone" class="form-control phone-mask"  aria-describedby="basic-default-phone" value="{{auth()->user()->mobile}}" />
            </div>
          </div>
         
        </div>
      </div>
    </div>
  </div>
  <input type="hidden" id="type_id" name="type_id" value="1">
  <input type="hidden" id="policy_vechile_class_type" name="policy_vechile_class_type" value="">
  <input type="hidden" id="act_price" name="details[act_price]" value="">
  <input type="hidden" id="tp_price" name="details[tp_price]" value="">
  <input type="hidden" id="comp_price" name="details[comp_price]" value="">
  <input type="hidden" id="total_cost" name="total_cost" value="">
  <!-- قسم التكملة للطلب -->
  <div class="row">
  <div class="col-xxl" id = 'complet-section'  >
    <div class="card mb-6">
      <div class="card-header d-flex align-items-center justify-content-between">
      </div>
      <div class="card-body">
        <!-- <form id="complet-form"> -->
          <div class="row mb-6">
            <label class="col-sm-3 col-form-label" for="start-insu"> بداية التأمين</label>
            <div class="col-sm-9">
              <input type="date" name="details[start_insurance_date]" id="startdate" class="form-control phone-mask"  aria-describedby="basic-default-phone" />
            </div>
          </div>
          <div class="row mb-6">
            <label class="col-sm-3 col-form-label" for="end-insu"> نهاية التأمين</label>
            <div class="col-sm-9">
              <input disabled type="date" name="details[end_insurance_date]" id="expiration-date" class="form-control phone-mask"  aria-describedby="basic-default-phone" />
            </div>
          </div>
          <div class="row mb-6">
            <label class="col-sm-3 col-form-label" for="calc">مجموع القسط</label>
            <div class="col-sm-9">
              <input disabled type="text" id="sum" class="form-control phone-mask"  aria-describedby="basic-default-phone" />
            </div>
          </div>
          <div class="row mb-6">
            <label class="col-sm-3 col-form-label" for="chase-number"> رقم الشصي </label>
            <div class="col-sm-9">
              <input type="text"  name ="details[chasi_number]" id="chase" class="form-control phone-mask"  aria-describedby="basic-default-phone" value ="{{$order?->vehicleInsuranceDetails?->chasi_number}}"/>
            </div>
          </div>
        
          <div class="row mb-6">
            <label class="col-sm-3 col-form-label" for="name"> اسماء المخولين في القيادة</label>
            <div class="col-sm-9">
              <input  type="text" name="details[who_can_drive_name]" id="namecandrive" class="form-control phone-mask"  aria-describedby="basic-default-phone" value="{{$order?->vehicleInsuranceDetails?->who_can_drive_name}}"/>
            </div>
          </div>
          <div class="row mb-6">
            <label class="col-sm-3 col-form-label" for="idcandrive"> ارقام هويات المخولين بالقيادة</label>
            <div class="col-sm-9">
              <input  type="text" name="details[who_can_drive_identity]" id="idcandrive" class="form-control phone-mask"  aria-describedby="basic-default-phone" vlaue="{{$order?->vehicleInsuranceDetails?->who_can_drive_identity}}" />
            </div>
          </div>
          <div class="row mb-6">
            <label class="col-sm-3 col-form-label" for="car-numver"> رقم لوحة المركبة </label>
            <div class="col-sm-9">
              <input type="text" id="car-number" name="details[plate_number]" class="form-control phone-mask"  aria-describedby="basic-default-phone" value="{{$order?->vehicleInsuranceDetails?->plate_number}}" />
            </div>
          </div>
          <div class="row mb-6">
            <label class="col-sm-3 col-form-label" for="car-type">  نوع المركبة </label>
            <div class="col-sm-9">
              <select class="form-select" name="details[vechile_type]" id="car-type" aria-label="Default select example">
                    <option value="siat">سيات</option>
                    <option value="bmw">بي ام </option>
                    <option value="od"> اودي </option>
              </select>
            </div>
          </div>
          <div class="row mb-6">
            <label class="col-sm-3 col-form-label" for="desiel-type">  نوع الوقود </label>
            <div class="col-sm-9">
              <select class="form-select"  name="details[feul_type]" id="desiel-type" aria-label="Default select example">
                <option value="1">بنزين </option>
                <option value="2"> سولار </option>
                <option value="3"> كهرباء </option>
              </select>
            </div>
          </div>
          <div class="row mb-6">
            <label class="col-sm-3 col-form-label">صور المركبة (7 صور)</label>
            <div class="col-sm-9">
              @for ($i = 1; $i <= 7; $i++)
                <div class="mb-2">
                  <input type="file" name="images[]" class="form-control" accept="image/*" onchange="previewImage(event, {{ $i }})">
                  <img id="preview-{{ $i }}" src="" alt="Preview" style="display:none; max-width: 100px; margin-top: 10px;" />
                </div>
              @endfor
            </div>
          </div>

      </div>
    </div>
  </div>
  </div>
 
</form>
@endsection