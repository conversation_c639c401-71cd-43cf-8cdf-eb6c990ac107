@extends('layouts/commonMaster' )
@php

/* Display elements */
$contentNavbar = false;
$containerNav = ($containerNav ?? 'container-xxl');
$isNavbar = ($isNavbar ?? false);
$isMenu = ($isMenu ?? false);
$isFlex = ($isFlex ?? false);
$isFooter = ($isFooter ?? true);

/* HTML Classes */
$navbarDetached = 'navbar-detached';

/* Content classes */
$container = ($container ?? 'container-xxl');
@endphp
@section('layoutContent')

@include('layouts/sections/wordpress-header')

<!-- Content -->
<div class="layout-wrapper layout-content-navbar">
    <div class="layout-container">
        <!-- Layout page -->
        <div class="layout-page">
            <div class="content-wrapper" style="padding:20px">
                @if ($isFlex)
                    <div class="{{$container}} d-flex align-items-stretch flex-grow-1 p-0">
                @else
                    <div class="{{$container}} flex-grow-1 container-p-y">
                @endif
                        @yield('content')
                    </div>
            </div>
        </div>
    </div>
</div>

@include('layouts/sections/wordpress-footer')

<!-- Now the menu appears AFTER header and footer -->
@include('layouts/sections/menu/verticalMenu')
<!--/ Content -->
@yield('scripts')
@endsection
