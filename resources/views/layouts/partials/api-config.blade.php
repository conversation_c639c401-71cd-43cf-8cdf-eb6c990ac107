<script>
    // API Configuration - Available globally
    window.API_CONFIG = {
        BASE_URL: '{{ env("API_URL", "http://***********:82") }}',
        ENDPOINTS: {
            RENEWAL_POLICY: '/renewal-policy',
            VEHICLE_USES: '/vechile-uses',
            VEHICLE_SPECIAL_USES: '/vechile-spical-uses',
            MOTOR_MODEL: '/motor-model'
        }
    };
    
    // Helper function to build API URLs
    window.buildApiUrl = function(endpoint, params = {}) {
        let url = window.API_CONFIG.BASE_URL + endpoint;
        
        // Add query parameters if provided
        if (Object.keys(params).length > 0) {
            const queryString = new URLSearchParams(params).toString();
            url += '?' + queryString;
        }
        
        return url;
    };
</script>
