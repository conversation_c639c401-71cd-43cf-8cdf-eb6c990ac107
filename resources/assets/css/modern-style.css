/* Modern Insurance Form Styling */
:root {
  --primary-color: #003D4C;
  --secondary-color: #0094B3;
  --success-color: #10B981;
  --danger-color: #EF4444;
  --background-color: #F8FAFC;
  --card-background: #FFFFFF;
  --text-color: #1E293B;
  --border-color: #E2E8F0;
  --input-background: #F8FAFC;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

body {
  background-color: var(--background-color);
  color: var(--text-color);
}

/* Form Container */
.insurance-form-container {
  max-width: 1000px;
  margin: 2rem auto;
  padding: 0 1.5rem;
}

/* Form Header */
.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-header h4 {
  color: var(--primary-color);
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.form-header p {
  color: #64748B;
  font-size: 1.1rem;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

/* Form Sections */
.form-section {
  background: var(--card-background);
  border-radius: 1rem;
  box-shadow: var(--shadow-md);
  margin-bottom: 2rem;
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: var(--transition);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.5s ease forwards;
}

.form-section:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-body {
  padding: 2rem;
}

/* Section Titles */
.section-title {
  color: var(--primary-color);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  position: relative;
  padding-right: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.section-title i {
  font-size: 1.5rem;
  color: var(--secondary-color);
}

.info-text {
  color: #64748B;
  font-size: 1rem;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

/* Form Controls */
.form-control, .form-select {
  border: 2px solid var(--border-color);
  border-radius: 0rem;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: var(--transition);
  background-color: var(--input-background);
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 61, 76, 0.1);
}

.form-control::placeholder {
  color: #94A3B8;
}

/* Input Groups */
.input-group {
  position: relative;
}

.input-group-text {
  background-color: var(--input-background);
  border: 2px solid var(--border-color);
  border-radius: 0.75rem;
  padding: 0.75rem 1rem;
  color: var(--secondary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 3rem;
}

.input-group-text i {
  font-size: 1.25rem;
}

.input-group .form-control {
  border-start-start-radius: 0;
  border-end-start-radius: 0;
}

.input-group .input-group-text {
  border-start-end-radius: 0;
  border-end-end-radius: 0;
  border-right: none;
}

/* Form Labels */
.form-label {
  font-weight: 500;
  color: var(--text-color);
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

/* Buttons */
.btn {
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border-radius: 0.75rem;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
}

.btn i {
  font-size: 1.25rem;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  transform: translateY(-1px);
}

/* Loading States */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Validation States */
.form-control.is-invalid, .form-select.is-invalid {
  border-color: var(--danger-color);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23dc3545' viewBox='0 0 16 16'%3E%3Cpath d='M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8 4a.905.905 0 0 0-.9.995l.35 3.507a.552.552 0 0 0 1.1 0l.35-3.507A.905.905 0 0 0 8 4zm.002 6a1 1 0 1 0 0 2 1 1 0 0 0 0-2z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: left 1rem center;
  background-size: 1.5rem;
}

.invalid-feedback {
  color: var(--danger-color);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Alert Styles */
.alert {
  border-radius: 0.75rem;
  padding: 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.alert i {
  font-size: 1.5rem;
}

.alert-success {
  background-color: #ECFDF5;
  border-color: #A7F3D0;
  color: #065F46;
}

.alert-danger {
  background-color: #FEF2F2;
  border-color: #FECACA;
  color: #991B1B;
}

.alert-heading {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

/* File Upload */
.file-upload {
  position: relative;
  display: inline-block;
  width: 100%;
}

.file-upload input[type="file"] {
  display: none;
}

.file-upload-label {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem;
  background-color: var(--input-background);
  border: 2px dashed var(--border-color);
  border-radius: 0.75rem;
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
}

.file-upload-label:hover {
  border-color: var(--primary-color);
  background-color: rgba(0, 61, 76, 0.05);
}

.file-upload-label i {
  font-size: 1.5rem;
  color: var(--secondary-color);
}

/* Radio and Checkbox */
.form-check {
  padding-right: 1.75rem;
  padding-left: 0;
  margin-bottom: 0.5rem;
}

.form-check-input {
  float: right;
  margin-right: -1.75rem;
  margin-left: 0;
  width: 1.25rem;
  height: 1.25rem;
  cursor: pointer;
}

.form-check-input:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.form-check-label {
  cursor: pointer;
}

/* RTL Specific Styles */
.rtl {
  direction: rtl;
  text-align: right;
}

.rtl .form-control, .rtl .form-select {
  text-align: right;
}

.rtl .btn i {
  margin-left: 0.5rem;
  margin-right: 0;
}

.rtl .input-group .form-control {
  border-radius: 0.75rem 0 0 0.75rem;
}

.rtl .input-group .input-group-text {
  border-radius: 0 0.75rem 0.75rem 0;
  border-left: none;
  border-right: 2px solid var(--border-color);
}

/* Grid System Improvements */
.row {
  margin-right: -1rem;
  margin-left: -1rem;
  row-gap: 1rem;
}

.col-md-6 {
  padding-right: 1rem;
  padding-left: 1rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .card-body {
    padding: 1.5rem;
  }
  
  .form-label {
    margin-bottom: 0.5rem;
  }
  
  .btn {
    width: 100%;
  }
  
  .form-section {
    margin-bottom: 1rem;
  }
  
  .row {
    margin-right: -0.5rem;
    margin-left: -0.5rem;
  }
  
  .col-md-6 {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  
  .form-header h4 {
    font-size: 1.5rem;
  }
  
  .form-header p {
    font-size: 1rem;
  }
} 