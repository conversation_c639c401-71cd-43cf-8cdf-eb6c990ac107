.social-icons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.social-icons a {
  font-size: 16px;
  color: #fff;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: 0.3s;
  text-decoration: none;
}

.facebook {
  background: #004d66;
}

.instagram {
  background: #004d66;
}

.x {
  background: #004d66;
}

.youtube {
  background: #004d66;
}

.social-icons a:hover {
  opacity: 0.8;
}

body {
  font-family: Arial, sans-serif;
}

header {
  background-color: rgba(0, 0, 0, 0.2);
  color: white;
  padding: 10px 20px;
}

nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.logo {
  size: 100px;
  font-size: 50px;
  font-weight: bold;
}

.menu {
  display: flex;
  gap: 20px;
}

.dropdown {
  position: relative;
}

.dropdown-content {
  display: none;
  position: absolute;
  background-color: white;
  color: #003d4c;
  min-width: 200px;
  box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
  z-index: 1;
  right: 0;
}

.dropdown-content a {
  padding: 10px;
  display: block;
  text-decoration: none;
  color: #003d4c;
}

.dropdown:hover .dropdown-content {
  display: block;
}

.menu a,
.dropdown > a {
  text-decoration: none;
  color: #003d4c;
  cursor: pointer;
}

footer {
  background-color: #333;
  color: #fff;
}

.container {
  background: url('back.jpeg') no-repeat center center;
  background-size: cover;
  color: #fff;
  padding: 20px;
  display: flex;
  justify-content: space-around;
  align-items: flex-start;
  flex-wrap: wrap;
}
.footer-section .links-container {
  display: flex;
  gap: 40px;
}
.footer-section h2 {
  font-size: 38px;
  font-weight: 600;
  color: #00b6cd;
}
.footer-section ul li a {
  color: white !important;
  /* Gold color for links */
  text-decoration: none !important;
  /* Remove underline */
  font-weight: bold;
}

.footer-section ul li a:hover {
  color: #ffffff !important;
  /* White on hover */
  text-decoration: none !important;
  /* Underline on hover */
}

.footer-section {
  min-width: 200px;
  margin: 10px;
  flex-direction: column;
}

.footer-section h3 {
  border-bottom: 2px solid #fff;
  padding-bottom: 5px;
}

.footer-section ul {
  list-style-type: none;
  padding: 0;
}

.footer-section ul li {
  margin: 5px 0;
}

/* Additional styling for a better layout */
.footer-section ul li a {
  transition:
    color 0.3s,
    text-decoration 0.3s;
  /* Smooth transition */
}

/* Style for footer bottom section */
.copyright {
  background: url('images/footer-bg.jpg') no-repeat center center;
  background-size: cover;
  color: #eee;
  text-align: center;
  padding: 10px;
  font-size: 0.9em;
}

.layout-menu-fixed:not(.layout-menu-collapsed) .layout-page,
.layout-menu-fixed-o {
  padding-right: 16.25rem !important;
  padding-left: inherit;
}
.layout-menu-fixed .layout-menu,
.layout-menu-fixed-offcanvas .layout-menu {
  right: 0 !important;
  left: inherit;
}
.form-select {
  background-position: left 0.9375rem center !important;
}

.layout-menu-fixed .layout-menu,
.layout-menu-fixed-offcanvas .layout-menu {
  position: absolute !important;
  top: 90px !important;
  /* bottom: -152px !important; */
  left: 0;
  margin-right: 0 !important;
  margin-left: 0 !important;
  height: 100vh;
}

@media (max-width: 768px) {
  /* Styles for mobile and tablet devices */
  body {
    /* background-color: lightblue; */
  }
  .layout-menu-fixed:not(.layout-menu-collapsed) .layout-page,
  .layout-menu-fixed-o {
    padding-right: 0px !important;
  }
  .layout-menu-fixed .layout-menu,
  .layout-menu-fixed-offcanvas .layout-menu {
    right: auto !important;
  }

  .content-wrapper {
    padding: 0px;
    font-size: 14px;
  }
}
