<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class FormsVehicleInsuranceDetail extends Model
{

    /**
     * The database table used by the model.
     *
     * @var string
     *///تابع لحادث
    protected $table = 'vehicle_insurance_details';

    protected $fillable = [
      'insurance_order_id', 'user_id', 'policy_location', 'policy_use', 'policy_spical_use',
      'policy_load', 'policy_cc', 'policy_inurance_vechile_price', 'policy_seats', 'policy_add_seats',
      'policy_prod_year', 'policy_days_perc', 'policy_vechile_class_type', 'policy_reg_date',
      'act_price', 'tp_price', 'comp_price', 'insured_full_name', 'insured_date_of_birth',
      'insured_identity', 'insured_phone', 'insured_location', 'insured_end_license_date', 'insured_emai',
      'start_insurance_date', 'end_insurance_date', 'who_can_drive_name', 'who_can_drive_identity',
      'total_cost', 'chasi_number', 'plate_number', 'vechile_type', 'feul_type',
      'left_image', 'right_image', 'back_image', 'front_image', 'driver_license_image',
      'chasi_image', 'identitiy_image', 'vechile_llicense_image','young_or_new_driver'
  ];

  public function insuranceOrder()
  {
      return $this->belongsTo(InsuranceOrder::class);
  }

}
