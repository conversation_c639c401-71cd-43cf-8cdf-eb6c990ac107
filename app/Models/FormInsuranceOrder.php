<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FormInsuranceOrder extends Model
{

    protected $table = 'insurance_orders';
    use SoftDeletes;

    protected $fillable = [
        'user_id', 'type_id', 'total_price', 'status', 'approved_by' , 'note_1'
    ];

    // Simulated ENUM for type_id
    public static function typeOptions()
    {
        return [
            1 => 'تأمين مركبة',
            2 => 'تامين منزل',
            3 => 'تأمين سفر',
            4 => 'تأمين شخصي',
            5 => 'تأمين المصاعد',
        ];
    }

    public function getTypeLabelAttribute()
    {
        return self::typeOptions()[$this->type_id] ?? 'Unknown';
    }



    public function vehicleInsuranceDetails()
    {
        return $this->hasOne(FormsVehicleInsuranceDetail::class, 'insurance_order_id');
    }
    public function TravelInsuranceDetails()
    {
        return $this->hasOne(FormsTravelInsuranceDetail::class, 'insurance_order_id');
    }
    public function HomeInsuranceDetails()
    {
        return $this->hasOne(FormsHomeInsuranceOrderDetail::class, 'insurance_order_id');
    }
    public function PersonalInsuranceDetails()
    {
        return $this->hasOne(FormsPersonalInsuranceDetail::class, 'insurance_order_id');
    }
     public function ElevatorInsuranceDetails()
    {
        return $this->hasOne(FormsElevatorInsurance::class, 'insurance_order_id');
    }
    
    

    // Relationship to User
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
