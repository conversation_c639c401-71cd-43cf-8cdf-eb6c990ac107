<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FormsIndividualInsurancePersonal extends Model
{
    use HasFactory;
    protected $table = 'individual_insurance_persons';
    protected $fillable = [
        'insurance_order_id','individual_insurance_detail_id','cust_aname',
        	'cust_lname','cus_user_id','cust_id','cust_mob',
        	'cust_dob','cust_address','cust_email','cust_branch'
    ];

    // Relationships
    public function insuranceOrder()
    {
        return $this->belongsTo(InsuranceOrder::class);
    }
}
