<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FormsPersonalInsuranceAnswer extends Model
{
    use HasFactory;
    protected $table = 'individual_answer_questions';

    protected $fillable = [
        'insurance_order_id',
        'individual_insurance_detail_id',
        'question_id',
        'question_text',
        'answer',
    ];
    public function insuranceOrder()
    {
        return $this->belongsTo(InsuranceOrder::class);
    }
}
