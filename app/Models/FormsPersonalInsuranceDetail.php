<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FormsPersonalInsuranceDetail extends Model
{
    use HasFactory;
    protected $table = 'individual_insurance_details';
    protected $fillable = [
        'insurance_order_id',
        'user_id',
        'p_pacc_program',
        'program_details',
        'p_si',
        'p_ins_start_dt',
        'p_ins_end_dt',
        'p_perc',
        'p_min_prem',
        'p_prem',
        'p_fees',
    ];
    public function insuranceOrder()
    {
        return $this->belongsTo(InsuranceOrder::class);
    }
    public function personsOreder()
    {
        return $this->hasMany(FormsIndividualInsurancePersonal::class, 'individual_insurance_detail_id');
    }
    public function qustionsOreder()
    {
        return $this->hasMany(FormsPersonalInsuranceAnswer::class, 'individual_insurance_detail_id');
    }
    
}
