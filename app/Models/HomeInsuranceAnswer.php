<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HomeInsuranceAnswer extends Model
{
    use HasFactory;
    protected $table = 'home_answer_qustion';
    protected $fillable = [
        'insurance_order_id',
        'home_insurance_details',
        'qustion_id',
        'question_adesc',
        'answer',
    ];
    public function QustionsHome()
    {
        return $this->belongsTo(FormsHomeInsuranceOrderDetail::class);
    }
}
