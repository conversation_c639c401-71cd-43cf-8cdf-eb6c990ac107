<?php
namespace App\Models;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class PassengerInsurance extends Model
{
/**
     * The database table used by the model.
     *
     * @var string
     *///تابع لحادث
     protected $table = 'travel_passengers';
     protected $fillable = [
        'insurance_order_id','travel_insurance_details_id','pass_seq','pass_passport_no','pass_aname','pass_lname',
        'passport_edate','pass_dob','pass_mob','pass_address','pass_prem','pass_passport_type'
    ];
    
    public function passengerInsurance()
    {
        return $this->belongsTo(FormsTravelInsuranceDetail::class);
    }

}