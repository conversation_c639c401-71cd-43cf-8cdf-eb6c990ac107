<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FormsTravelInsuranceDetail extends model{
/**
     * The database table used by the model.
     *
     * @var string
     *///تابع لحادث
     protected $table = 'travel_insurance_details';
     protected $fillable = [
        'insurance_order_id','user_id','p_travel_to','p_ins_start_dt',
        'p_ins_end_dt',	'p_covid',	'p_si',	'p_age_70','p_age_75',
        'p_age_90',	'p_age__more_90','p_age_5',	'p_prem',
        'p_fees',	'number_of_travelers','insured_full_name',
        'customer_identity','insured_identity','insured_date_of_birth',
        'insured_email','insured_phone'
    ];
    public function insuranceOrder()
    {
        return $this->belongsTo(InsuranceOrder::class);
    }
    public function passengersOreder()
    {
        return $this->hasMany(PassengerInsurance::class, 'travel_insurance_details_id');
    }

}