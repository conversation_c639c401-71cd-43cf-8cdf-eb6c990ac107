<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FormsElevatorInsurance extends Model
{
    use HasFactory;
      protected $table = 'elevator_insurance_details'; 

    protected $fillable = [
        'insurance_order_id',
        'user_id',
        'p_elevator_type',
        'p_elevator_cat',
        'p_si',
        'p_no_passengers',
        'p_prod_year',
        'p_no_floors',
        'p_ins_start_dt',
        'p_ins_end_dt',
        'p_perc',
        'p_min_prem',
        'p_prem',
        'p_fees',
        'location',
        'insured_full_name',
        'customer_identity',
        'insured_identity',
        'insured_date_of_birth',
        'insured_email',
        'insured_phone',
        'building_name',
        'building_type',
        'full_address',
        'city',
        'street',
        'building_number',
        'coverage_ceiling'
        
    ];
   public function insuranceOrder()
    {
        return $this->belongsTo(InsuranceOrder::class);
    }

      
    
}
