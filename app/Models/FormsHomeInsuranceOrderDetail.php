<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FormsHomeInsuranceOrderDetail extends Model
{
    use HasFactory;
    protected $table = 'home_insurance_details';
    protected $fillable = [
        'insurance_order_id',
        'user_id',
        'p_home_type',
        'p_home_cat',
        'p_build_si',
        'p_content_si',
        'p_ins_start_dt',
        'p_ins_end_dt',
        'p_perc',
        'p_min_prem',
        'p_prem',
        'p_fees',
        'insured_full_name',
        'customer_identity',
        'insured_identity',
        'insured_date_of_birth',
        'insured_email',
        'insured_phone',
        'structure_types',
        'location',
        'address',
        'street',
        'building_number',
        'building_floor_type',
        'building_roof_type',
        'building_wall_type',
        'building_area',
        'land_area',
        'mortgage_holder',
        'mortgage_end_date',
        'notes',
    ];

    // Relationships
    public function insuranceOrder()
    {
        return $this->belongsTo(InsuranceOrder::class);
    }
     public function imagesOreder()
    {
        return $this->hasMany(InsuranceOrderImage::class, 'insurance_details_id');
    }
     public function qustionsOreder()
    {
        return $this->hasMany(HomeInsuranceAnswer::class, 'home_insurance_details');
    }
}
