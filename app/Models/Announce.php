<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Announce extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     *///تابع لحادث
    protected $table = 'iclaims_announces';

    protected $casts = [
        'iclm_inves_file_status' => 'integer',
        'iclm_acc_inj_cnt' => 'integer',
        'iclm_no' => 'integer',
        'iclm_id' => 'integer',
        'iclm_acc_place' => 'integer',
        'iclm_driver_gender' => 'integer',
        'iclm_acc_type' => 'integer',
        'iclm_acc_reason' => 'integer',
        'iclm_ins_st_dt' => 'date:Y-m-d',
        'iclm_driver_lic_1stdate' => 'date:Y-m-d',
        'iclm_reg_dt' => 'date:Y-m-d',
        'iclm_driver_lic_sdate' => 'date:Y-m-d',
        'iclm_driver_lic_edate'=> 'date:Y-m-d',
        'iclm_acc_dt'=> 'date:Y-m-d',
        'iclm_reg_dt'=> 'date:Y-m-d',
        'iclm_driver_desc_dt'=> 'date:Y-m-d',
        'iclm_mot_lic_end_dt' => 'date:Y-m-d',
        'clm_denamo_dt' =>'date:Y-m-d',
        'iclm_inves_rep_dt' => 'date:Y-m-d',
    ];
    protected $appends = ['file_status_data', 'location_data'];

    public function fileStatus()
    {
        return $this->belongsTo(
            ClaimInvestigationFileStatus::class,
            'iclm_inves_file_status', // Foreign key in iclaims_announces
            'CLM_INVS_FILE_STATUS'   // Primary key in T_CLM_INVS_FILE_STATUS
        );
    }
 
    public function location()
    {
        return $this->belongsTo(
            ClaimLocation::class,
            'iclm_acc_place', // Foreign key in iclaims_announces
            'loc_code'   // Primary key in T_CLM_INVS_FILE_STATUS
        );
    }

    public function getFileStatusDataAttribute()
    {
        return $this->fileStatus ? $this->fileStatus : [];
    }

    public function getLocationDataAttribute()
    {
        return $this->location ? $this->location : [];
    }
}
