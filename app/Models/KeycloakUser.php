<?php

namespace App\Models;

use Illuminate\Contracts\Auth\Authenticatable;

class KeycloakUser implements Authenticatable
{
    protected $userData;

    public function __construct($userData)
    {
        $this->userData = $userData;
    }

    public function getAuthIdentifierName()
    {
        return 'id';
    }

    public function getAuthIdentifier()
    {
        return $this->userData->sub; // Keycloak user id
    }

    public function getAuthPassword()
    {
        return null; // No password, as you're authenticating with a token
    }

    public function getRememberToken()
    {
        return null; // No remember token
    }

    public function setRememberToken($value)
    {
        // Not needed
    }

    public function getRememberTokenName()
    {
        return null; // Not needed
    }
}
