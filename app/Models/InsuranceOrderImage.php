<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InsuranceOrderImage extends Model
{
     protected $table = 'insurance_order_images';
    use HasFactory;
     protected $fillable = [
        'insurance_order_id',
        'insurance_type_id',
        'user_id',
        'insurance_details_id',
        'image_path',
        'type',
    ];
    /**
     * Relationships (optional but recommended if you use them)
     */
    public function insuranceOrder()
    {
        return $this->belongsTo(InsuranceOrder::class);
    }
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}


   

  
