<?php
use Illuminate\Support\Facades\Http;

if (!function_exists('getInsuTypeById')) {
    function getInsuTypeById($id)
    {
        $types = [
            1 => 'تأمين مركبات',
            2 => 'تأمين منزلي',
            3 => 'تأمين سفر',
            4 => 'تامين شخصي',
            5=> 'تأمين مصاعد'
        ];

        return $types[$id] ?? 'Unknown';
    }

}
if (!function_exists('getLookups')) {
    function getLookups($endpoint){
        $response = Http::get(env('API_URL','http://82.213.0.2:8000/').$endpoint);
        return $response->json();
    }
}

if (!function_exists('showImage')) {
  function showImage(string $blobName): string
    {
        $container = env('AZURE_STORAGE_CONTAINER');
        $accountName = env('AZURE_STORAGE_NAME');
        $accountKey = env('AZURE_STORAGE_KEY');
        $connectionString = "DefaultEndpointsProtocol=https;AccountName={$accountName};AccountKey={$accountKey}";
        $sasToken = env('AZURE_SAS_Token');
        //'sv=2024-11-04&ss=bfqt&srt=sco&sp=rwdlacupiytfx&se=2028-06-22T20:22:13Z&st=2025-06-22T12:22:13Z&spr=https&sig=lPZkR5xR5DPwPzB1L%2FGxcpwKJWA4AmYFrfiMZWG1fsQ%3D';
        $blobUrl = "https://{$accountName}.blob.core.windows.net/{$container}/{$blobName}?{$sasToken}";
        return $blobUrl;
    }
}