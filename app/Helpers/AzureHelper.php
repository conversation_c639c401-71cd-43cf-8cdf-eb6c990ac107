<?php
namespace App\Helpers;

use Illuminate\Http\UploadedFile;

class AzureHelper
{
    public static function uploadImage(UploadedFile $file)
    {

      $originalName = $file->getClientOriginalName();
      $cleanName = preg_replace('/[^a-zA-Z0-9_\.-]/', '_', $originalName);
      $filename = time() . '_' . $cleanName;
      $connectionString = env('AZURE_STORAGE_CONNECTION_STRING');
      $container = env('AZURE_STORAGE_CONTAINER');

      // استخراج AccountName و AccountKey من connection string
      preg_match('/AccountName=([^;]+)/', $connectionString, $accountMatches);
      preg_match('/AccountKey=([^;]+)/', $connectionString, $keyMatches);

      if (!$accountMatches || !$keyMatches) {
          return response()->json([
              'success' => false,
              'message' => 'Invalid Azure connection string.',
          ], 500);
      }

      $accountName = $accountMatches[1];
      $accountKey = $keyMatches[1];
      $url = "https://{$accountName}.blob.core.windows.net/{$container}/{$filename}";

      // محتوى الملف وحجمه
      $fileContent = file_get_contents($file->getRealPath());
      $contentLength = strlen($fileContent);
      $mimeType = $file->getMimeType();

      // تاريخ الطلب بصيغة GMT
      $date = gmdate('D, d M Y H:i:s T');

      // رؤوس Azure المطلوبة
      $canonicalizedHeaders =
          "x-ms-blob-type:BlockBlob\n" .
          "x-ms-date:$date\n" .
          "x-ms-version:2020-04-08\n";

      // المورد المخصص
      $canonicalizedResource = "/{$accountName}/{$container}/{$filename}";

      // تصحيح Content-Length (يجب أن يكون "0" إذا الملف فارغ، هنا افتراضياً ليس فارغ)
      $contentLengthStr = $contentLength > 0 ? $contentLength : "0";

      // بناء سلسلة التوقيع حسب معايير Azure
      $stringToSign =
          "PUT\n" .                  // VERB
          "\n" .                    // Content-Encoding
          "\n" .                    // Content-Language
          $contentLengthStr . "\n" .// Content-Length
          "\n" .                    // Content-MD5
          $mimeType . "\n" .        // Content-Type
          "\n" .                    // Date (فارغ لأننا نستخدم x-ms-date)
          "\n" .                    // If-Modified-Since
          "\n" .                    // If-Match
          "\n" .                    // If-None-Match
          "\n" .                    // If-Unmodified-Since
          "\n" .                    // Range
          $canonicalizedHeaders .
          $canonicalizedResource;

      // حساب التوقيع HMAC SHA256
      $signature = base64_encode(
          hash_hmac('sha256', $stringToSign, base64_decode($accountKey), true)
      );
      $authorization = "SharedKey {$accountName}:{$signature}";
      // تنفيذ طلب CURL مع ترويسة التوثيق
      $ch = curl_init();
      curl_setopt($ch, CURLOPT_URL, $url);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
      curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
      curl_setopt($ch, CURLOPT_POSTFIELDS, $fileContent);
      curl_setopt($ch, CURLOPT_HTTPHEADER, [
          "Authorization: {$authorization}",
          "x-ms-blob-type: BlockBlob",
          "x-ms-date: {$date}",
          "x-ms-version: 2020-04-08",
          "Content-Type: {$mimeType}",
          "Content-Length: {$contentLengthStr}",
      ]);

      // يمكنك تعطيل التحقق من SSL للتجربة (لا تستخدمه في الإنتاج)
      curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
      curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
      curl_setopt($ch, CURLOPT_SSLVERSION, CURL_SSLVERSION_TLSv1_2);
      curl_setopt($ch, CURLOPT_TIMEOUT, 30);

      $response = curl_exec($ch);
      $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
      $error = curl_error($ch);
      curl_close($ch);

      if ($error) {
          \Log::error('Direct cURL Upload Error: ' . $error);
          return response()->json([
              'success' => false,
              'message' => 'Upload failed: ' . $error,
          ], 500);
      }

      if ($httpCode >= 200 && $httpCode < 300) {
          return response()->json([
              'success' => true,
              'message' => 'Image uploaded successfully!',
              'filename' => $filename,
              'url' => $url,
          ]);
      } else {
          \Log::error("Direct cURL Upload HTTP Error: {$httpCode} - {$response}");
          return response()->json([
              'success' => false,
              'message' => "Upload failed: HTTP {$httpCode}",
              'details' => $response,
          ], 500);
      }
    }

    function showImage(string $blobName): string
    {
        $container = env('AZURE_STORAGE_CONTAINER');
        $accountName = env('AZURE_STORAGE_NAME');
        $accountKey = env('AZURE_STORAGE_KEY');
        $connectionString = "DefaultEndpointsProtocol=https;AccountName={$accountName};AccountKey={$accountKey}";
        $sasToken = env('AZURE_SAS_Token');
        //'sv=2024-11-04&ss=bfqt&srt=sco&sp=rwdlacupiytfx&se=2028-06-22T20:22:13Z&st=2025-06-22T12:22:13Z&spr=https&sig=lPZkR5xR5DPwPzB1L%2FGxcpwKJWA4AmYFrfiMZWG1fsQ%3D';
        $blobUrl = "https://{$accountName}.blob.core.windows.net/{$container}/{$blobName}?{$sasToken}";
        return $blobUrl;
    }
}