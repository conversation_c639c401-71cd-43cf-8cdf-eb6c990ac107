<?php

namespace App\Http\Controllers\dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Announce;
use App\Models\ClaimInspectorPhotos;


class Analytics extends Controller
{
  public function index(Request $request)
  {
    
    // $announces = Announce::query();
    // $inspectors = ClaimInspectorPhotos::query();

    // if ($request->filled('from_date') && $request->filled('to_date')) {
    //     $announces->whereBetween('created_at', [
    //         $request->from_date . ' 00:00:00',
    //         $request->to_date . ' 23:59:59'
    //     ]);
    //     $inspectors->whereBetween('created_at', [
    //       $request->from_date . ' 00:00:00',
    //       $request->to_date . ' 23:59:59'
    //   ]);
    // }
    // $announces_data = $announces->orderBy('created_at', 'desc')->paginate(10);

    // $statistic = [
    //   'total_announces' => $announces->count(),
    //   'total_inspectors' => $inspectors->count()
    // ];
    return view('content.dashboard.dashboards-analytics', 
    // compact('statistic', 'announces_data')
  
  );
  }
}
