<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\AzureBlobService;

class AzureImageController extends Controller
{
    public function upload(Request $request, AzureBlobService $azure)
    {
        $request->validate(['image' => 'required|image|max:2048']);
        $result = $azure->uploadImage($request->file('image'));
        return response()->json($result);
    }

    public function show(Request $request, AzureBlobService $azure)
    {
        $request->validate(['filename' => 'required|string']);
        $url = $azure->generateSasUrl($request->input('filename'));
        return response()->json(['url' => $url]);
    }
}
