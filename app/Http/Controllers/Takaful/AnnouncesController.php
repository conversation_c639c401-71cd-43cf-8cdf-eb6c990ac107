<?php

namespace App\Http\Controllers\Takaful;

use App\Models\ClaimInspectorPhotos;
use App\Models\Announce;
use App\Models\Witness;
use App\Models\Injured;
use App\Models\ThirdParty;
use App\Models\ThirdPartyPhoto;
use App\Models\Policie;
use Illuminate\Http\Request;
use App\Services\S3StorageService;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\Controller;
use \Log;
use Carbon\Carbon;

class AnnouncesController extends Controller
{

    // Get all users
    public function index()
    {

        $announces = Announce::orderBy('created_at', 'desc')->paginate(20);

        return view('content.takaful.investigator.list', compact('announces'));
    }

    // Get a single user
    public function show($id)
    {
        $announce = Announce::find($id);
        $timeInMinutes = $announce->iclm_acc_time; // مثال للقيمة المخزنة
        $hours = floor($timeInMinutes / 60);
        $minutes = $timeInMinutes % 60;
        $originalTime = Carbon::createFromTime($hours, $minutes)->format('h:i A');
        $announce['iclm_acc_time'] = $originalTime;
        // dd($announce->timeOfAnnouncement);
        $timeInMinutes2 = $announce->iclm_reg_time; // مثال للقيمة المخزنة
        $hours2 = floor($timeInMinutes2 / 60);
        $minutes2 = $timeInMinutes2 % 60;
        $originalTime2 = Carbon::createFromTime($hours2, $minutes2)->format('h:i A');
        $announce['iclm_reg_time'] = $originalTime2;

        $witness = Witness::where('iclm_no', $id)->get();
        $injured = Injured::where('iclm_no', $id)->get();
        $third_party = ThirdParty::where('iclm_no', $id)->get();

    
        $policie = Policie::where('ICLM_CHASI_NO', $announce->iclm_chasi)->first();
   
        $data = [
           'announce'=>$announce,
           'witness' => $witness,
           'injured' => $injured,
           'third_party' => $third_party,
           'policie' => $policie
        ];
        return $announce ? response()->json(
            $data
            ) : response()->json(['message' => 'announce not found'], 404);
    }

    public function filterByPlateNo($number) 
    {
        $announce = Announce::where('iclm_plate_no', $number)->first();
        $announce['clm_invs_file_status_desc'] = $announce->fileStatus->clm_invs_file_status_desc ?? null;
        $announce['loc_name'] = $announce->location->loc_name ?? null;
       
        // $policie = Policie::where('ICLM_CHASI_NO', $announce->iclm_chasi)->first();
        $data = [
           'announce'=>$announce,
        ];
        return $announce ? response()->json(
            $data
            ) : response()->json(['message' => 'announce not found'], 404);
    }

}