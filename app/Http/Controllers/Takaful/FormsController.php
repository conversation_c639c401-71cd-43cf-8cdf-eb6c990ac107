<?php

namespace App\Http\Controllers\Takaful;

use App\Models\ClaimInspectorPhotos;
use App\Models\Announce;
use App\Models\Witness;
use App\Models\Injured;
use App\Models\ThirdParty;
use App\Models\ThirdPartyPhoto;
use App\Models\Policie;
use Illuminate\Http\Request;
use App\Services\S3StorageService;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\Controller;
use \Log;
use Carbon\Carbon;

class FormsController extends Controller
{

    // Get all users
    public function index()
    {
        return view('content.takaful.takaful-forms.car-clalc-forms');
    }

    
    // Get a single user
    public function show($id)
    {

    }

    public function filterByPlateNo($number) 
    {

    }

}