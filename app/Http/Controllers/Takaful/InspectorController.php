<?php

namespace App\Http\Controllers\Takaful;
use App\Http\Controllers\Controller;
use App\Models\ClaimInspectorPhotos;
use Illuminate\Http\Request;
use App\Services\S3StorageService;
use Illuminate\Support\Facades\Storage;
use \Log;

class InspectorController extends Controller
{
    
    // Get all users
    public function index()
    {
        $inspectors = ClaimInspectorPhotos::orderBy('created_at', 'desc')->paginate(20);

        $groupedData = $inspectors->mapToGroups(function ($item) {
            return [
                $item->mph_id => [
                    'mph_id' => $item->mph_id,
                    'mph_chasi' => $item->mph_chasi,
                    'mph_plate_no' => $item->mph_plate_no,
                    'mph_user' => $item->mph_user,
                    'photos' => [
                        'mph_photo_seq' => $item->mph_photo_seq,
                        'mph_photo_type' => $item->mph_photo_type,
                        'mph_photo_file' => $item->mph_photo_file,
                    ]
                ]
            ];
        });

        // Convert grouped data to a simple collection for pagination
        $groupedData = collect($groupedData);

        return view('content.takaful.inspectors.list', compact('groupedData', 'inspectors'));

    }

    // Get a single user
    public function show($id)
    {
        $mph_id = ClaimInspectorPhotos::find($id)->mph_id;

        $photos = ClaimInspectorPhotos::all();
        $groupedData = $photos->mapToGroups(function ($item) {
            return [
                $item->mph_id => [
                    'mph_id' => $item->mph_id,
                    'mph_chasi' => $item->mph_chasi,
                    'mph_plate_no' => $item->mph_plate_no,
                    'mph_user' => $item->mph_user,
                    'photos' => [
                        'mph_photo_seq' => $item->mph_photo_seq,
                        'mph_photo_type' => $item->mph_photo_type,
                        'mph_photo_file' => $item->mph_photo_file,
                    ]
                ]
            ];
        });
        
        return $user ? response()->json($user) : response()->json(['message' => 'User not found'], 404);
    }

    public function store(Request $request)
    {
        // $files = Storage::disk('s3')->allFiles();
        // return response()->json($files);
        $this->validate($request, [
            'chase_number' => 'required',
            'images' => 'required'
        ]);

        $maxMphId = ClaimInspectorPhotos::max('MPH_ID');
        $number = $maxMphId ? $maxMphId + 1 : 1;
        // return response()->json(['status' => true, 'images'=> $request->images]); 


        foreach ($request->images as $index => $row) {
            Log::debug("index". $index);
            Log::debug("row", $row);
            if ($request->hasFile("images.$index.path") && $request->file("images.$index.path")->isValid()) {
                $imageFile = $request->file("images.$index.path");
                $fileName = time() . '-' . $index . '.' . $imageFile->getClientOriginalExtension();
                Log::debug("fileName :". $fileName);
                $relativePath = 'images/' . $fileName;
                // try {
                    $index = 0; // Adjust index if handling multiple images
                    if (!$request->hasFile("images.$index.path")) {
                        return response()->json(['status' => false, 'error' => 'No image file found in request.'], 400);
                    }
            
                    // $imageFile = $request->file("images.$index.path");
                    Log::debug("imageFile :". $imageFile);
                    if (!$imageFile->isValid()) {
                        return response()->json(['status' => false, 'error' => 'Uploaded file is not valid.'], 400);
                    }
            
                    $fileName = time() . '-' . $index . '.' . $imageFile->getClientOriginalExtension();
                    $relativePath = 'images/' . $fileName; // Relative path for database storage
                    $destinationPath = public_path('images'); // Destination folder
                    // Move the uploaded file
                    $imageFile->move($destinationPath, $fileName);
            
                    // Save record to database
                    $image = new ClaimInspectorPhotos();
                    $image->MPH_ID = $number;
                    $image->MPH_CHASI = $request->chase_number;
                    $image->MPH_PLATE_NO = $request->car_number;
                    $image->MPH_USER = $request->user_id;
                    // return response()->json($row);
                    $image->MPH_PHOTO_SEQ = $row['sec'];
                    $image->MPH_PHOTO_TYPE = $row['type'];
                    $image->MPH_PHOTO_FILE = url('/images/' . $fileName); // Store full URL
            
                    $image->save();
                    Log::info("data saved :", $image->toArray());
                // } catch (\Exception $e) {
                //     return response()->json([
                //         'status' => false,
                //         'error' => 'Image upload failed!',
                //         'exception' => $e->getMessage(),
                //         'line' => $e->getLine(),
                //         'file' => $e->getFile()
                //     ], 500);
                // }
            }
        }
        return response()->json(['status' => true]);
    }

    // Update a user
    public function update(Request $request, $id)
    {
        $user = User::find($id);
        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        $user->update($request->only(['name', 'email', 'password']));
        return response()->json($user);
    }

    // Delete a user
    public function destroy($id)
    {
        $user = User::find($id);
        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        $user->delete();
        return response()->json(['message' => 'User deleted successfully']);
    }
}
