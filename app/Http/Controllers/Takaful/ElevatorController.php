<?php

namespace App\Http\Controllers\Takaful;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use App\Models\FormInsuranceOrder;
use App\Models\FormsElevatorInsurance;
use Illuminate\Http\Request;
use DB;

class ElevatorController extends Controller
{

    public function index(Request $request)
    {
        $type_id = 5;
        $user_id = auth()->id();
        if($request->type_id){
            $type_id = $request->type_id;
        } 
        $orders = FormInsuranceOrder::with('user')
            ->with('ElevatorInsuranceDetails')
            ->where('user_id', $user_id)
            ->where('type_id', $type_id)
            ->orderBy('created_at', 'desc')
            ->paginate(10); // Add pagination with 10 items per page

        return view('content.takaful.takaful-forms.elevator.list-order', compact('orders'));
    }
    public function create()
    {
        $buildingcategory = getLookups('elevator-building-category');
        $elevatorcategory = getLookups('elevator-category');
        $elevatormanuftype = getLookups('elevator-manuf-type');
        $locations = getLookups('insurance-locations');
        return view('content.takaful.takaful-forms.elevator.create-page', compact(
                    'buildingcategory',
                    'elevatorcategory',
                    'elevatormanuftype',
                    'locations'
        ));
    }
    public function edit($id)
    {
     $buildingcategory = getLookups('elevator-building-category');
     $elevatorcategory = getLookups('elevator-category');
     $elevatormanuftype = getLookups('elevator-manuf-type');
     $locations = getLookups('insurance-locations');
     $order = FormInsuranceOrder::with('user')->with('ElevatorInsuranceDetails')->where('id', $id)->first();
     return view('content.takaful.takaful-forms.elevator.edit-page',compact('order',
                    'buildingcategory',
                    'elevatorcategory',
                    'elevatormanuftype',
                    'locations'
    ));
    }
    public function show($id)
    {
     $item_policy = Http::get(env('API_URL').''.$id); 
     $buildingcategory = getLookups('elevator-building-category');
     $elevatorcategory = getLookups('elevator-category');
     $elevatormanuftype = getLookups('elevator-manuf-type');
     $locations = getLookups('insurance-locations');
     return view('content.takaful.takaful-forms.elevator.external-edit',compact('item_policy',
                    'buildingcategory',
                    'elevatorcategory',
                    'elevatormanuftype',
                    'locations'
    ));
    }

           

  
}