<?php

namespace App\Http\Controllers\Takaful;

use App\Http\Controllers\Controller;
use App\Models\FormInsuranceOrder;
use App\Models\FormsTravelInsuranceDetail;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;
use DB;

class IndividualOrderController extends Controller
{

    public function index(Request $request)
    {
        $type_id = 4;
        $user_id = auth()->id();
        if($request->type_id){
            $type_id = $request->type_id;
        } 
        
        $query = FormInsuranceOrder::with('user')
            ->with('PersonalInsuranceDetails')
            ->where('type_id', $type_id)
            ->orderBy('created_at', 'desc');
            
        if(auth()->user()->role === 'customer'){
            $query->where('user_id', $user_id);
        }
        
        $orders = $query->paginate(10); // Add pagination with 10 items per page

        return view('content.takaful.takaful-forms.individual.main-page', compact('orders'));
    }
    public function create()
    {
      
      $programs= getLookups('personal-insurance-programs');
      $questions = getLookups('personal-insurance-questions');
    
      return view('content.takaful.takaful-forms.individual.calc-page',compact('questions','programs'));
    }
    public function edit($id)
    {
      $programs = getLookups('personal-insurance-programs');
      $questions = getLookups('personal-insurance-questions');
      $order = FormInsuranceOrder::with(['user', 'PersonalInsuranceDetails.personsOreder', 'PersonalInsuranceDetails.qustionsOreder'])
        ->where('id', $id)
        ->firstOrFail();
      return view('content.takaful.takaful-forms.individual.edit-page', compact('order', 'programs', 'questions'));
    }

    public function update(Request $request, $id)
    {
        $order = FormInsuranceOrder::findOrFail($id);
        
        // Update order details
        $order->update([
            'total_price' => $request->total_cost ?? $order->total_price
        ]);

        // Update personal insurance details
        if ($details = $request->input('details')) {
            $order->PersonalInsuranceDetails()->update($details);
        }

        return redirect()->route('individual-orders.index')
            ->with('success', 'تم تحديث التأمين الشخصي بنجاح');
    }
    public function show($id)
    {
      $item_policy = Http::get(env('API_URL').''.$id);     
      $programs = getLookups('personal-insurance-programs');
      $questions = getLookups('personal-insurance-questions');
      return view('content.takaful.takaful-forms.individual.external-edit', compact('programs', 'questions'));
    }
             

}
