<?php

namespace App\Http\Controllers\Takaful;

use App\Http\Controllers\Controller;
use App\Models\FormInsuranceOrder;
use App\Models\FormsVehicleInsuranceDetail;
use App\Models\FormsHomeInsuranceOrderDetail;
use App\Models\FormsTravelInsuranceDetail;
use App\Models\PassengerInsurance;
use App\Models\HomeInsuranceAnswer;
use App\Models\InsuranceOrderImage;
use App\Models\FormsPersonalInsuranceDetail;
use App\Models\FormsIndividualInsurancePersonal;
use App\Models\FormsPersonalInsuranceAnswer;
use App\Models\FormsElevatorInsurance;
use App\Models\VehicleInsuranceDetail;
use App\Helpers\AzureHelper;
use App\Http\Requests\StoreInsuranceOrderRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;
use DB;

class InsuranceOrderController extends Controller
{

    public function index(Request $request)
    {
        $type_id = 1;
        $user_id = auth()->id();
        if($request->type_id){
            $type_id = $request->type_id;
        }

        $query = FormInsuranceOrder::with('user')
            ->with('vehicleInsuranceDetails')
            ->where('type_id', $type_id)
            ->orderBy('created_at', 'desc');

        if(auth()->user()->role === 'customer'){
            $query->where('user_id', $user_id);
        }

        $orders = $query->paginate(10); // Add pagination with 10 items per page

        return view('content.takaful.takaful-forms.vehicle.main-order-form', compact('orders'));
    }

  public function store(StoreInsuranceOrderRequest $request)
  {
      $order = FormInsuranceOrder::create([
        'user_id' => auth()->id(),
        'type_id' => $request->type_id,
        'total_price' => $request->total_cost,
        'status' => 0,
        'approved_by' => null
      ]);

      $detailData = $request->input('details');
      $images = $request->file('images') ?? [];
      $imageColumns = [
        'left_image',
        'right_image',
        'back_image',
        'front_image',
        'driver_license_image',
        'chasi_image',
        'identitiy_image',
        'vechile_llicense_image'
      ];

      //store images for vechile
      if($request->type_id ==1){
        foreach ($images as $index => $file) {
          if (isset($imageColumns[$index])) {
            $result = AzureHelper::uploadImage($file);
            $data = json_decode($result->getContent(), true);
            if (array_key_exists('filename', $data)) {
              $detailData[$imageColumns[$index]] = $data['filename'];
            }
            // $filename = time() . '_' . $file->getClientOriginalName();
            // $path = $file->storeAs('public/uploads', $filename);
            // $detailData[$imageColumns[$index]] = 'uploads/' . $filename;
          }
        }
      }
      $detailData['insurance_order_id'] = $order->id;
      $detailData['user_id'] = auth()->id();
      $customers = $request->input('customers', []);
      $customers['insurance_order_id']=$order->id;
      $passengers = $request->input('passengers', []);
      $passengers['insurance_order_id'] = $order->id;
      $questions = $request->input('questions', []);
      $questions['insurance_order_id']=$order->id;
      $this->createDetailByType($request->type_id , $detailData, $passengers , $questions ,$customers ,$images);
   // Determine redirect URL based on type_id
   switch ($request->type_id) {
    case 1:
        return redirect()->route('insurance-orders.index');
        break;
    case 2:
        return redirect()->route('home-orders.index');
        break;
    case 3:
        return redirect()->route('travel-orders.index');
        break;
    case 4:
        return redirect()->route('individual-orders.index');
        break;
    case 5:
        return redirect()->route('elevator-orders.index');
        break;
    default:
    return redirect()->route('insurance-orders.index');
        }   

      
        }
  

   function createDetailByType($typeId, $data ,$passengers,$question ,$customers , $images )
  {
    switch ((int) $typeId) {
        case 1:
            FormsVehicleInsuranceDetail::create($data);
             break;
        case 2:
            $form = FormsHomeInsuranceOrderDetail::create($data);
            $insurance_order_id = $question['insurance_order_id'];
            $questionAnswers = $question ;
            $insertData = [];
            $questionList = getLookups('home-questions');
            
            foreach ($questionAnswers as $questionId => $answer) {
              $matchingQuestion = collect($questionList)->firstWhere('question_id', $questionId);
              $insertData[] = [
                  'insurance_order_id' => $insurance_order_id,
                  'home_insurance_details' => $form->id,
                  'question_id' => (int) $questionId,
                  'question_text' => $matchingQuestion['question_adesc']??"",
                  'answer' => $answer,
              ];
            }
            $insertImage=[];
            // foreach ($images as $index => $file ) {
            //   $filename = time() . '_' . $file->getClientOriginalName();
            //   $path = $file->storeAs('/uploads', $filename);
            //   $insertImage[] = [
            //     'insurance_order_id' => $insurance_order_id,
            //     'insurance_type_id'=>2,
            //     'user_id'=>auth()->id(),
            //     'insurance_details_id'=>$form->id,
            //     'image_path'=>$path	,
            //     'type'=>'home'
            //   ]  ;
            // }
            foreach ($images as $index => $file ) {
              $result = AzureHelper::uploadImage($file);
              $data = json_decode($result->getContent(), true);
             
              $insertImage[] = [
                  'insurance_order_id' => $insurance_order_id,
                  'insurance_type_id'=>2,
                  'user_id'=>auth()->id(),
                  'insurance_details_id'=>$form->id,
                  'image_path' => $data['filename'],
                  'type'=>'home'
              ];
            }
            InsuranceOrderImage::insert($insertImage);
            HomeInsuranceAnswer::insert($insertData);
            break;
        case 3:
            $form = FormsTravelInsuranceDetail::create($data);
            $insurance_order_id = $passengers['insurance_order_id'];
            $passengerList = array_filter($passengers, 'is_array');

            // Add the foreign key to each passenger
            foreach ($passengerList as &$passenger) {
                $passenger['travel_insurance_details_id'] = $form->id;
                $passenger['insurance_order_id'] = $insurance_order_id;
            }
             $insertImage=[];
            // foreach($images as $index => $file ){
            // $filename = time() . '_' . $file->getClientOriginalName();
            // $path = $file->storeAs('/uploads', $filename);
            // $insertImage[] = [
            // 'insurance_order_id' => $insurance_order_id,
            // 'insurance_type_id'=>3,
            // 'user_id'=>auth()->id(),
            // 'insurance_details_id'=>$form->id,
            // 'image_path'=>$path	,
            // 'type'=>'passenger'
            // ]  ;
            // }
            foreach ($images as $index => $file ) {
              $result = AzureHelper::uploadImage($file);
              $data = json_decode($result->getContent(), true);
              $insertImage[] = [
                  'insurance_order_id' => $insurance_order_id,
                  'insurance_type_id'=>3,
                  'user_id'=>auth()->id(),
                  'insurance_details_id'=>$form->id,
                  'image_path'=>$data['filename'],
                  'type'=>'passenger'
              ];
            }
            PassengerInsurance::insert($passengerList);
            InsuranceOrderImage::insert($insertImage);

            break;
        case 4:
           $form= FormsPersonalInsuranceDetail::create($data);
           $insurance_order_id = $customers['insurance_order_id'];
           $customerList = array_filter($customers, 'is_array');

           // Add the foreign key to each customer
           foreach ($customerList as &$customer) {
               $customer['individual_insurance_detail_id'] = $form->id;
               $customer['insurance_order_id'] = $insurance_order_id;
           }

           $questionAnswers = $question;
           $insertData = [];
           $questionList = getLookups('personal-insurance-questions');
           foreach ($questionAnswers as $questionId => $answer) {
               // Find the matching question from questionList
               $matchingQuestion = collect($questionList)->firstWhere('question_id', $questionId);

               $insertData[] = [
                   'insurance_order_id' => $insurance_order_id,
                   'individual_insurance_detail_id' => $form->id,
                   'question_id' => (int) $questionId,
                   'question_text' => $matchingQuestion['question_adesc'] ?? 'غير معرف',
                   'answer' => $answer,
               ];
           }
           FormsPersonalInsuranceAnswer::insert($insertData);
           FormsIndividualInsurancePersonal::insert($customerList);

            break;
        case 5:
            FormsElevatorInsurance::create($data); break;
    }
}

        public function create(){
    $response = Http::get(env('API_URL').'vechile-uses');
    $items = $response->json();
    $locations = getLookups('insurance-locations');
    $vehcilType = getLookups('vechile-type');
    $enginesource = getLookups('engine-source');

        
    return view('content.takaful.takaful-forms.vehicle.car-clalc-forms', compact('items','locations','vehcilType','enginesource'));
  }
 
  
  public function edit($id)
  {
    $order = FormInsuranceOrder::with('user')->with('vehicleInsuranceDetails')->where('id', $id)->first();
    $response = Http::get(env('API_URL').$id);
    $data = $response->json();
    $response = Http::get(env('API_URL').'vechile-uses');
    $items = $response->json();
     if($order->type_id == 1){
        return view('content.takaful.takaful-forms.vehicle.edit-vehicel-insu', compact('order','items'));
     }
   }
   public function show($id)
  {
    $item_policy = Http::get(env('API_URL').'last-policy?chasiNo='.$id);
    $policy = $item_policy->json();
    $locations = getLookups('insurance-locations');
    $vehcilType = getLookups('vechile-type');
    $enginesource = getLookups('engine-source');
    $response = Http::get(env('API_URL').'vechile-uses');
    $items = $response->json();
        return view('content.takaful.takaful-forms.vehicle.external-edit', compact('items','locations','vehcilType','enginesource','policy'));
   }
  public function update(Request $request, $id)
  {
      $order = FormInsuranceOrder::findOrFail($id);
      $orderData = $request->only(['type_id', 'total_price', 'status', 'approved_by']);
      $order->update($orderData);
      $detailData = $request->input('details'); // Send 'details' in request body
      $detailModel = $this->getInsuranceDetailModel($order->type_id, $order->id);
      if ($detailModel) {
          $detailModel->update($detailData);
      }
      return response()->json([
          'order' => $order,
          'details' => $detailModel
      ]);
  }
  public function updateStatus(Request $request,$id){
    $order = FormInsuranceOrder::findOrFail($id);
    if ($request->has('status')) {
        $order->status = $request->status;
        $order->approved_by = auth()->user()->id;
        $order->note_1 = $request->note;
        $order->save();
        return response()->json([
            'status' => $order->status,

        ]);
    }else{
        $order->approved_by = auth()->user()->id;
        $order->note_2 = $request->note;
        $order->save();
         return response()->json([
            'note' => $order->note_2,
        ]);
    }

  }
//     public function updateOtherNote(Request $request,$id){
//     $order = FormInsuranceOrder::findOrFail($id);
//         $order->approved_by = auth()->user()->id;
//         $order->note_2 = $request->note;
//         $order->save();
//         return response()->json([
//             'note' => $order->note_2,
//         ]);
//   }


  private function getInsuranceDetailModel($typeId, $orderId)
  {
      switch ($typeId) {
          case 1:
              return FormsVehicleInsuranceDetail::where('insurance_order_id', $orderId)->first();
          case 2:
              return FormsHomeInsuranceDetail::where('insurance_order_id', $orderId)->first();
          case 3:
              return FormsTravelInsuranceDetail::where('insurance_order_id', $orderId)->first();
          case 4:
              return FormsPersonalInsuranceDetail::where('insurance_order_id', $orderId)->first();
          case 5:
              return FormsElevatorInsurance::where('insurance_order_id', $orderId)->first();
          default:
              return null;
      }
  }

  public function renew($id)
  {
      // Get the original order with all possible relationships
      $originalOrder = FormInsuranceOrder::with([
          'VehicleInsuranceDetails',
          'HomeInsuranceDetails',
          'TravelInsuranceDetails',
          'PersonalInsuranceDetails',
          'ElevatorInsuranceDetails'
      ])->where('id', $id)->firstOrFail();

      try {
          DB::beginTransaction();

          // Create new order
          $newOrder = FormInsuranceOrder::create([
              'user_id' => auth()->id(),
              'type_id' => $originalOrder->type_id,
              'total_price' => $originalOrder->total_price,
              'status' => 0,
              'approved_by' => null
          ]);

          // Copy details based on insurance type
          switch ($originalOrder->type_id) {
              case 1: // Vehicle
                  if ($details = $originalOrder->VehicleInsuranceDetails) {
                      $newDetails = $details->replicate();
                      $newDetails->insurance_order_id = $newOrder->id;
                      $newDetails->save();
                  }
                  break;

              case 2: // Home
                  if ($details = $originalOrder->HomeInsuranceDetails) {
                      $newDetails = $details->replicate();
                      $newDetails->insurance_order_id = $newOrder->id;
                      $newDetails->save();

                      // Copy home insurance questions and answers
                      $originalQuestions = HomeInsuranceAnswer::where('insurance_order_id', $originalOrder->id)
                          ->where('home_insurance_details', $details->id)
                          ->get();

                      foreach ($originalQuestions as $question) {
                          $newQuestion = $question->replicate();
                          $newQuestion->insurance_order_id = $newOrder->id;
                          $newQuestion->home_insurance_details = $newDetails->id;
                          $newQuestion->save();
                      }

                      // Copy home insurance images if they exist
                      $originalImages = InsuranceOrderImage::where('insurance_order_id', $originalOrder->id)
                          ->where('insurance_type_id', 2)
                          ->where('insurance_details_id', $details->id)
                          ->get();

                      foreach ($originalImages as $image) {
                          $newImage = $image->replicate();
                          $newImage->insurance_order_id = $newOrder->id;
                          $newImage->insurance_details_id = $newDetails->id;
                          $newImage->save();
                      }
                  }
                  break;

              case 3: // Travel
                  if ($details = $originalOrder->TravelInsuranceDetails) {
                      $newDetails = $details->replicate();
                      $newDetails->insurance_order_id = $newOrder->id;
                      $newDetails->save();
                  }
                   // Copy home insurance images if they exist
                   $originalImages = InsuranceOrderImage::where('insurance_order_id', $originalOrder->id)
                   ->where('insurance_type_id', 3)
                   ->where('insurance_details_id', $details->id)
                   ->get();

               foreach ($originalImages as $image) {
                   $newImage = $image->replicate();
                   $newImage->insurance_order_id = $newOrder->id;
                   $newImage->insurance_details_id = $newDetails->id;
                   $newImage->save();
               }
               PassengerInsurance::where('insurance_order_id', $originalOrder->id)->get();
               foreach($originalPassengers as $passenger){
                $newPassenger = $passenger->replicate();
                $newPassenger->insurance_order_id = $newOrder->id;
                $newPassenger->travel_insurance_details_id = $newDetails->id;
                $newPassenger->save();
               }
                  break;

              case 4: // Individual
                  if ($details = $originalOrder->PersonalInsuranceDetails) {
                      $newDetails = $details->replicate();
                      $newDetails->insurance_order_id = $newOrder->id;
                      $newDetails->save();
                  }
                  FormsPersonalInsuranceAnswer::where('insurance_order_id', $originalOrder->id)->get();
                  foreach($originalQuestions as $question){
                    $newQuestion = $question->replicate();
                    $newQuestion->insurance_order_id = $newOrder->id;
                    $newQuestion->individual_insurance_detail_id = $newDetails->id;
                    $newQuestion->save();
                  }
                  break;

              case 5: // Elevator
                  if ($details = $originalOrder->ElevatorInsuranceDetails) {
                      $newDetails = $details->replicate();
                      $newDetails->insurance_order_id = $newOrder->id;
                      $newDetails->save();
                  }
                  break;

              default:
                  throw new \Exception('نوع التأمين غير معروف');
          }

          DB::commit();

          return response()->json([
              'success' => true,
              'message' => 'تم تجديد الطلب بنجاح',
              'order_id' => $newOrder->id
          ]);

      } catch (\Exception $e) {
          DB::rollback();
          return response()->json([
              'success' => false,
              'message' => 'حدث خطأ أثناء تجديد الطلب: ' . $e->getMessage()
          ], 500);
      }
  }

//   public function show($id)
//   {
//       $order = FormInsuranceOrder::with('user', 'vehicleInsuranceDetails')->findOrFail($id);
//       // You can create a dedicated view or reuse the vehicle details view
//       return view('content.takaful.takaful-forms.vehicle.show-order', compact('order'));
//   }
}
