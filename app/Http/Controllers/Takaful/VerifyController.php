<?php
namespace App\Http\Controllers\Takaful;

use Illuminate\Http\Request;
use App\Services\SmsServices;
use Illuminate\Support\Facades\Auth;

class VerifyController extends Controller
{
    protected $sms;
    public function __construct(SmsService $sms){
        $this->sms = $sms;
    }
    public function sendVerificationCode(){
        $user = Auth::user();
        $code = rand(1000,9999);

        $user-> otp = $code;
        $user->save();

        $sent = $this->sms->send($user->phone , "test otp" ,'رمز التحقق الخاص بك : ' . $code);

       if ($sent) {
            return redirect()->route('otp.page')->with('success', 'تم إنشاء الحساب بنجاح. يرجى إدخال رمز التفعيل الذي تم إرساله.');
        } else {
            return response()->json(['message' => 'فشل في إرسال الرسالة.'], 500);
        }

    }

    public function verifyCode(Request $request){
        $request->validate(['code'=>'required|numeric']);
        $user = Auth::user();
        if($user->otp == $request->code){
            $user->otp = null;
            $user->save();
                        return response()->json(['message' => 'تم التحقق من رقم الهاتف بنجاح.']);

        }
                return response()->json(['message' => 'رمز التحقق غير صحيح.'], 400);

    }


}
