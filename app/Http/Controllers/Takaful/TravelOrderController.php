<?php

namespace App\Http\Controllers\Takaful;

use App\Http\Controllers\Controller;
use App\Models\FormInsuranceOrder;
use App\Models\FormsTravelInsuranceDetail;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;
use DB;

class TravelOrderController extends Controller
{

    public function index(Request $request)
    {
        $type_id = 3;
        $user_id = auth()->id();
        if($request->type_id){
            $type_id = $request->type_id;
        } 
        
        $query = FormInsuranceOrder::with('user')
            ->with('TravelInsuranceDetails')
            ->where('type_id', $type_id)
            ->orderBy('created_at', 'desc');
            
        if(auth()->user()->role === 'customer'){
            $query->where('user_id', $user_id);
        }
        
        $orders = $query->paginate(10); // Add pagination with 10 items per page

        return view('content.takaful.takaful-forms.travel.main-page', compact('orders'));
    }
    public function create()
    {
        $locations = getLookups('travel-locations');
        $travelcountries = getLookups('travel-countries');
        return view('content.takaful.takaful-forms.travel.create-travel' ,compact('travelcountries','locations'));
    }
    public function edit($id)
    {           
        $locations = getLookups('insurance-locations');
        $travelcountries = getLookups('travel-countries');
        $order = FormInsuranceOrder::with('user')->with('TravelInsuranceDetails.passengersOreder')->where('id', $id)->first();
        return view('content.takaful.takaful-forms.travel.edit-travel', compact('order','locations','travelcountries'));
    }
     public function show($id)
    {       
        $item_policy = Http::get(env('API_URL').''.$id);    
        $locations = getLookups('insurance-locations');
        $travelcountries = getLookups('travel-countries');
        $order = FormInsuranceOrder::with('user')->with('TravelInsuranceDetails.passengersOreder')->where('id', $id)->first();
        return view('content.takaful.takaful-forms.travel.external-edit', compact('order','locations','travelcountries','item_policy'));
    }

 
  
}
