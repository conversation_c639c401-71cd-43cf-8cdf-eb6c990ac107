<?php

namespace App\Http\Controllers\Takaful;

use App\Models\VehicleInsuranceDetail;
use Illuminate\Http\Request;

class VehicleInsuranceDetailController extends Controller
{
    public function index()
    {
        return VehicleInsuranceDetail::all();
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'insurance_order_id' => 'required|exists:insurance_orders,id',
            'user_id' => 'required|exists:users,id',
            // Add validation rules as needed
        ]);

        $record = VehicleInsuranceDetail::create($request->all());
        return response()->json($record, 201);
    }

    public function show($id)
    {
        return VehicleInsuranceDetail::findOrFail($id);
    }

    public function update(Request $request, $id)
    {
        $record = VehicleInsuranceDetail::findOrFail($id);
        $record->update($request->all());
        return response()->json($record);
    }

    public function destroy($id)
    {
        VehicleInsuranceDetail::destroy($id);
        return response()->json(['message' => 'Deleted successfully']);
    }
}
