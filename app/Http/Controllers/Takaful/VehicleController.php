<?php

namespace App\Http\Controllers\Takaful;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;


class VehicleController extends Controller
{
  public function index()
  {
    // $response = Http::get('http://82.213.0.2:8000/vechile-uses'); // Replace with real API
    // $items = $response->json();
    return view('content.takaful.takaful-forms.main-order-form');
  }
  public function calcForms()
  {
    $response = Http::get(env('API_URL', 'http://4.209.27.43:82') . '/vechile-uses');
    $items = $response->json();
    return view('content.takaful.takaful-forms.car-calc-forms', compact('items','vehcilType'));
  }
  public function edit(){
    return view('content.takaful.admin.preview-vehicel-insu');
    
  }


  public function fetchVehcilSpesUses($id)
  {
      $response = Http::get(env('API_URL', 'http://4.209.27.43:82') . '/vechile-spical-uses?spu_use_id='.$id);
      $data = $response->json();
      return $data;
  }
   public function fetchVehcilModel($id)
  {
      $response = Http::get(env('API_URL', 'http://4.209.27.43:82') . '/motor-model?mot_brand_code='.$id);
      $data = $response->json();
      return $data;
  }
}