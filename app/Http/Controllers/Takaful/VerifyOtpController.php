<?php
namespace App\Http\Controllers\Takaful;
use App\Services\SmsServices;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;


class VerifyOtpController extends Controller
{
   
    protected $sms;
    public function __construct(SmsServices $sms){
        $this->sms = $sms;
    }
    public function index(){
         return view('content.authentications.otp-verification');
    }
    public function sendVerificationCode(){
       
        $user = Auth::user();
        $code = rand(100000, 999999);
        $user->otp = $code;
        $user->save();
        $sent = $this->sms->send($user->mobile ,"test otp",'رمز التحقق الخاص بك  - Your otp is : '.$code);


       if ($sent) {
       
            return redirect()->route('otp.page')->with('success', 'تم إنشاء الحساب بنجاح. يرجى إدخال رمز التفعيل الذي تم إرساله.');
        } else {
            return response()->json(['message' => 'فشل في إرسال الرسالة.'], 500);
        }

    }



    public function verifyCode(Request $request){
        $request->validate(['code'=>'required|numeric']);
        $user = Auth::user();
        if($user->otp == $request->code){
            $user->otp = null;
            $user->save();
            return redirect()->route('insurance-orders.index');
        }else{
 return redirect()->back()->withErrors([
            // 'login' => 'حدث خطأ',
            'details' =>  'الرمز الذي تم ادخاله غير صحيح'
           ]);
        }

    }


}
