<?php
namespace App\Http\Controllers\Takaful;
use App\Http\Controllers\Controller;
use App\Models\FormInsuranceOrder;
use App\Models\InsuranceOrderImage;
// use App\Models\FormsTravelInsuranceDetail;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;
use DB;

class HomeOrderController extends Controller
{

    public function index(Request $request)
    {
        $type_id = 2;
        $user_id = auth()->id();
        if($request->type_id){
            $type_id = $request->type_id;
        }

        $query = FormInsuranceOrder::with('user')
            ->with('HomeInsuranceDetails')
            ->where('type_id', $type_id)
            ->orderBy('created_at', 'desc');

        if(auth()->user()->role === 'customer'){
            $query->where('user_id', $user_id);
        }

        $orders = $query->paginate(10); // Add pagination with 10 items per page

        return view('content.takaful.takaful-forms.home.main-page', compact('orders'));
    }
    public function create()
    {
        $hometype = getLookups('home-building-types');
        $walltype = getLookups('home-wall-types');
        $archtype = getLookups('home-arch-types');
        $ownershiptype = getLookups('home-ownership-types');
        $floortype = getLookups('home-floor-types');
        $ceiling = getLookups('home-ceiling-types');
        $locations = getLookups('insurance-locations');
        $qustions = getLookups('home-questions');
        return view('content.takaful.takaful-forms.home.create-home',compact(
                    'hometype',
                    'walltype',
                    'archtype',
                    'ownershiptype',
                    'floortype',
                    'ceiling',
                    'locations',
                    'qustions'
                  ));
    }
    public function edit($id)
    {
        try {
            $hometype = getLookups('home-building-types');
            $walltype = getLookups('home-wall-types');
            $archtype = getLookups('home-arch-types');
            $ownershiptype = getLookups('home-ownership-types');
            $floortype = getLookups('home-floor-types');
            $ceiling = getLookups('home-ceiling-types');
            $locations = getLookups('insurance-locations');

            $order = FormInsuranceOrder::with('user')
                ->with('HomeInsuranceDetails.imagesOreder', 'HomeInsuranceDetails.qustionsOreder')
                ->where('id', $id)
                ->firstOrFail();

            // Safely get images with error handling
            $images = InsuranceOrderImage::where('insurance_order_id', $id)
                ->where('type', 'home')
                ->get();

            return view('content.takaful.takaful-forms.home.edit-order', compact(
                'order',
                'hometype',
                'walltype',
                'archtype',
                'ownershiptype',
                'floortype',
                'ceiling',
                'locations',
                'images'
            ));
        } catch (\Exception $e) {
            // Log the error if needed
            \Log::error('Error in HomeOrderController@edit: ' . $e->getMessage());

            // Return with error message
            return redirect()->back()
                ->with('error', 'Unable to load the order details. Please try again.');
        }
    }


 public function show($id)
    {
            $item_policy = Http::get(env('API_URL').''.$id); 
            $hometype = getLookups('home-building-types');
            $walltype = getLookups('home-wall-types');
            $archtype = getLookups('home-arch-types');
            $ownershiptype = getLookups('home-ownership-types');
            $floortype = getLookups('home-floor-types');
            $ceiling = getLookups('home-ceiling-types');
            $locations = getLookups('insurance-locations');
            return view('content.takaful.takaful-forms.home.edit-order', compact(
                'item_policy',
                'hometype',
                'walltype',
                'archtype',
                'ownershiptype',
                'floortype',
                'ceiling',
                'locations',
            ));

        }

}
