<?php

namespace App\Http\Controllers\authentications;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\SmsServices;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;




class ForgotPasswordBasic extends Controller
{
    protected $sms;
    public function __construct(SmsServices $sms){
        $this->sms = $sms;
    }
  public function index()
  {
    return view('content.authentications.auth-forgot-password-basic');
  }

  public function sendVerificationCode(Request $request){
    $mobile = \App\Models\User::where('identity' ,$request->identity)->value('mobile');
    $code = rand(100000, 999999);
      DB::table('password_resets')->updateOrInsert(
        ['identity' => $request->identity],
        [
            'code' => $code,
            'expires_at' => now()->addMinutes(10),
            'updated_at' => now(),
            'created_at' => now()
        ]
    );
    $sent = $this->sms->send($mobile,"رمز التحقق",'رمز التحقق الخاص بك  - Your otp is : '.$code);
      if ($sent) {
    
        return redirect()->route('verify.otp.page')->with('success', 'تم ارسال الرمز بنجاح. يرجى إدخال رمز التفعيل الذي تم إرساله.');
    } else {
        return response()->json(['message' => 'فشل في إرسال الرسالة.'], 500);
    }
}
   public function verifyOtp(Request $request)
    {
        $request->validate([
            'identity' => 'required|string',
            'code' => 'required|string'
        ]);

        $identity = $request->identity;
        $code = $request->code;

        $record = DB::table('password_resets')->where('identity', $identity)->first();

        if (!$record) {
           
          return redirect()->back()->withErrors([
            'details' =>  'الرمز الذي تم ادخاله غير صحيح'
           ]);        }

//         if ($record->code !== $code || now()->greaterThan($record->expires_at)) {
//  return redirect()->back()->withErrors([
//             'details' =>  'الرمز الذي تم ادخاله  منتهي الصلاحية'
//            ]);  }
               
        return redirect()->route('reset.page');
    }

     public function resetPassword(Request $request)
    {
        $request->validate([
            'identity' => 'required|string',
            'new_password' => 'required|string|min:8'
        ]);

        $username = $request->identity;
        $otp = $request->otp;
        $newPassword = $request->new_password;

        // Check OTP again (double security)
        // $record = DB::table('password_resets')->where('username', $username)->first();

        // if (!$record || $record->otp !== $otp || now()->greaterThan($record->expires_at)) {
        //     return response()->json(['error' => 'Invalid or expired OTP'], 400);
        // }

        // Reset password in Keycloak
        if ($this->updateKeycloakPassword($username, $newPassword)) {
            DB::table('password_resets')->where('identity', $username)->delete();

            return redirect()->route('auth-login-basic');
        } else {
            return response()->json(['error' => 'Failed to reset password'], 500);
        }
    }

    /**
     * Keycloak API to reset password
     */
    private function updateKeycloakPassword($username, $newPassword)
    {
        try {
            $client = new Client();
            $baseurl = env('KEYCLOAK_LOGIN_URL');
            $realm = env('KEYCLOAK_REALM');
            $clientIdEnv = 'flutter-app';

            // 1. Get admin token
           $tokenResponse = $client->post(env('KEYCLOAK_BASE_URL') . '/realms/master/protocol/openid-connect/token', [
                'form_params' => [
                    'grant_type' => 'password',
                    'client_id' => 'admin-cli',
                    'username' => env('KEYCLOAK_ADMIN_USER'),
                    'password' => env('KEYCLOAK_ADMIN_PASSWORD'),
                ]
            ]);
            $token = json_decode($tokenResponse->getBody(), true)['access_token'];
            // 2. Get user ID   
            $userRes =  $client->get(env('KEYCLOAK_BASE_URL') . "/admin/realms/$realm/users", [
                'headers' => ['Authorization' => "Bearer $token"],
                'query' => ['username' => $username]
            ]);
            
            $userData = json_decode($userRes->getBody(), true);
            

            if (empty($userData)) return false;
            $userId = $userData[0]['id'];
           

            // 3. Reset password
            $client->put(env('KEYCLOAK_BASE_URL') . "/admin/realms/$realm/users/{$userId}/reset-password", [
                'headers' => [
                    'Authorization' => "Bearer {$token}",
                    'Content-Type'  => 'application/json'
                ],
                'json' => [
                    'type' => 'password',
                    'value' => $newPassword,
                    'temporary' => false
                ]
            ]);
            
            \App\Models\User::where('identity', $username)
            ->update(['password' => bcrypt($newPassword)]);

            return redirect()->route('auth-login-basic');

        } catch (\Exception $e) {
            Log::error("Keycloak reset failed: " . $e->getMessage());
            return false;
        }
    }

}
