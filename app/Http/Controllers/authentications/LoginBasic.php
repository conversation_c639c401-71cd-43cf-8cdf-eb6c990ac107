<?php

namespace App\Http\Controllers\authentications;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Takaful\VerifyOtpController;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Session;


class LoginBasic extends Controller
{
  public function index()
  {
    return view('content.authentications.auth-login-basic');
  }
  public function logout()
  {
      Session::forget('keycloak_token');
      return redirect()->route('/auth/login');
  }
   // عملية تسجيل الدخول باستخدام Keycloak


public function login(Request $request)
{
    
    $request->validate([
        'identity' => 'required',
        'password' => 'required',
    ], [
        'userID.required' => 'رقم الهوية مطلوب',
        'password.required' => 'كلمة المرور مطلوبة',
    ]);


    try {
     
        $client = new Client();
        $baseurl = env('KEYCLOAK_LOGIN_URL');

        $response = $client->post($baseurl, [
            'form_params' => [
                'grant_type' => 'password',
                'client_id' => 'flutter-app',
                'username' => $request->identity,
                'password' => $request->password,
            ],
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
        ]);

        $data = json_decode($response->getBody()->getContents(), true);
       
        // Save token in session or wherever needed
        $credentials = $request->only('identity', 'password');

        if (Auth::attempt($credentials)) {
          
            $user = Auth::user();
            Session::put('keycloak_token', $data['access_token']);
            
        } else {
          return redirect()->back()->withErrors([
            // 'login' => 'حدث خطأ',
            'details' =>  'اسم المستخدم او كملة المرور غير صحيحه'
           ]);
        }
       
    return app(VerifyOtpController::class)->sendVerificationCode();

            // return redirect()->route('otp.page')->with('success', 'تم إنشاء الحساب بنجاح. يرجى إدخال رمز التفعيل الذي تم إرساله.');

        // return redirect()->route('insurance-orders.index');

    } catch (\GuzzleHttp\Exception\RequestException $e) {
        $error = $e->getResponse()->getBody()->getContents();
        return redirect()->back()->withErrors([
            // 'login' => 'حدث خطأ',
            'details' =>  'اسم المستخدم او كملة المرور غير صحيحه'
        ]);
    }
}
}
