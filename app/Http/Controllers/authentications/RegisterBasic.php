<?php

namespace App\Http\Controllers\authentications;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use GuzzleHttp\Exception\ClientException;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class RegisterBasic extends Controller
{
  public function index()
  {
    return view('content.authentications.auth-register-basic');
  }
  public function register(Request $request)
    {
        $request->validate([
          'idcard' => 'required|min:9|max:9',
            'username' => 'required',
            'email' => 'required|email',
            'password' => 'required|min:6',
        ]);

        $realm = env('KEYCLOAK_REALM');
        $clientIdEnv = 'flutter-app';
        $spilName = explode(' ' ,$request->username);
        $firstName =$spilName[0] ;
        $lastNmae=end($spilName);
        try {

            $client = new Client();

            // 1. Get Admin Token
            $tokenResponse = $client->post(env('KEYCLOAK_BASE_URL') . '/realms/master/protocol/openid-connect/token', [
                'form_params' => [
                    'grant_type' => 'password',
                    'client_id' => 'admin-cli',
                    'username' => env('KEYCLOAK_ADMIN_USER'),
                    'password' => env('KEYCLOAK_ADMIN_PASSWORD'),
                ]
            ]);
            $accessToken = json_decode($tokenResponse->getBody(), true)['access_token'];

            // 2. Create User
            $createUser = $client->post(env('KEYCLOAK_BASE_URL') . "/admin/realms/$realm/users", [
                'headers' => [
                    'Authorization' => "Bearer $accessToken",
                    'Content-Type'  => 'application/json',
                ],
                'json' => [
                    'username' => $request->idcard,
                    'email' => $request->email,
                    'firstName'  => $firstName,
                    'lastName'   => $lastNmae,
                    'enabled' => true,
                    'credentials' => [
                        [
                            'type' => 'password',
                            'value' => $request->password,
                            'temporary' => false
                        ]
                    ]
                ]
            ]);
            // 3. Get Created User ID
            $users = $client->get(env('KEYCLOAK_BASE_URL') . "/admin/realms/$realm/users", [
                'headers' => ['Authorization' => "Bearer $accessToken"],
                'query' => ['username' => $request['id-card']]
            ]);
            $userId = json_decode($users->getBody(), true)[0]['id'];
            // 4. Get Client ID
            $clients = $client->get(env('KEYCLOAK_BASE_URL') . "/admin/realms/$realm/clients", [
                'headers' => ['Authorization' => "Bearer $accessToken"],
            ]);
            $clientsList = json_decode($clients->getBody(), true);
            $clientUuid = collect($clientsList)->firstWhere('clientId', $clientIdEnv)['id'];
            // 5. Get Client Role (e.g., default-role or custom)
            $response = $client->get(env('KEYCLOAK_BASE_URL') . "/admin/realms/$realm/clients/$clientUuid/roles/customer", [
              'headers' => [
                  'Authorization' => "Bearer $accessToken",
                  'Content-Type' => 'application/json',
              ],
          ]);
          $role = json_decode($response->getBody(), true);


            // 6. Assign Role to User
            $client->post(env('KEYCLOAK_BASE_URL') . "/admin/realms/$realm/users/$userId/role-mappings/clients/$clientUuid", [
              'headers' => [
                  'Authorization' => "Bearer $accessToken",
                  'Content-Type' => 'application/json',
              ],
              'json' => [
                  [
                      'id' => $role['id'],
                      'name' => $role['name']
                  ]
              ],
          ]);
          User::Create([
            'fullname' => $request->username,
            'email'=>$request->email,
            'mobile'=>$request->phone,
            'date_of_birth'=>$request->dateofbirth,
            'identity' =>$request->idcard,
            'password'=>$request->password,
          ]);

            // Redirect to OTP entry page after registration
          return redirect()->route('auth-login-basic');

        } catch (\Exception $e) {
          $errorMessage = 'فشلت عملية  انشاء الحساب ';
          if (str_contains($e->getMessage(), 'User exists with same username')) {
              $errorMessage = 'رقم الهوية مستخدم سابقا';
          }
         if ($e->errorInfo[1] == 1062) {
        // Duplicate entry
        $errorMessage = 'رقم الهاتف مستخدم سابقا .';}
        return redirect()->back()->withErrors([
            // 'login' => 'حدث خطأ',
            'details' =>  $errorMessage
        ]);
        }
    }


}
