<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreInsuranceOrderRequest extends FormRequest
{
    public function rules()
    {
        $rules = [
            // 'type_id' => 'required|in:1,2,3,4,5',
            // 'total_price' => 'nullable|numeric',
            // 'status' => 'boolean',
            // 'approved_by' => 'nullable|exists:users,id',
            // 'details' => 'required|array'
        ];

        switch ((int) $this->type_id) {
            case 1: // Vehicle
                // $rules['details.insurance_order_id'] = 'nullable';
                // $rules['details.user_id'] = 'required|exists:users,id';
                // $rules['details.policy_location'] = 'required|string';
                // $rules['details.policy_use'] = 'required|numeric';
                // $rules['details.policy_cc'] = 'required|numeric';
                // $rules['details.policy_prod_year'] = 'required|numeric';
                // $rules['details.policy_reg_date'] = 'required|date';
                // $rules['details.act_price'] = 'required|numeric';
                // $rules['details.tp_price'] = 'required|numeric';
                break;

            case 2: // Home
                // $rules['details.property_address'] = 'required|string';
                // $rules['details.property_value'] = 'required|numeric';
                break;

            case 3: // Travel
                // $rules['details.destination'] = 'required|string';
                // $rules['details.travel_date'] = 'required|date';
                break;

            case 4: // Personal
                // $rules['details.name'] = 'required|string';
                // $rules['details.identity'] = 'required|string';
                break;

            case 5: // Elevator
                // $rules['details.building_address'] = 'required|string';
                // $rules['details.num_floors'] = 'required|integer';
                break;
        }

        return $rules;
    }
}
