<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class RedirectIfNotAuthenticated
{
    public function handle(Request $request, Closure $next)
    {
      
      if ($request->routeIs('auth-login-basic')) {
        return $next($request);
      }
      $user = auth()->user();
      if (!Session::has('keycloak_token')) {
       
            return redirect()->route('auth-login-basic');
        
          
      }
      return $next($request);
    }
}
