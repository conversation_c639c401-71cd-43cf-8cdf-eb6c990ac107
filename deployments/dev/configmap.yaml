apiVersion: v1
kind: ConfigMap
metadata:
  name: takafuldev-wf-config
  namespace: takaful-dev
data:
  APP_DEBUG: "true"
  APP_ENV: "production"
  APP_KEY: "base64:xDEjXM3rRMfoL15ZlbUYbieH2G4ozEWZFRCjE90Wal0="
  APP_LOG_LEVEL: "debug"
  APP_NAME: "Takaful Webform"
  APP_URL: "http://takaful-dev-webform.srv.aeliasoft.com"
  BROADCAST_DRIVER: "log"
  CACHE_DRIVER: "file"
  QUEUE_DRIVER: "sync"
  SESSION_DRIVER: "file"
  SESSION_LIFETIME: "120"
  DB_CONNECTION: "oracle"
  DB_DATABASE: "TAKAFUL_DEV_WEBFORMS"
  DB_HOST: "deployments.srv.aeliasoft.com"
  DB_PASSWORD: "UT73LOswOP"
  DB_PORT: "32578"
  DB_SERVICE_NAME: "XEPDB1"
  DB_USERNAME: "TAKAFUL_DEV_WEBFORMS"
  REDIS_HOST: "localhost"
  REDIS_PORT: "6379"
  KEYCLOAK_BASE_URL: "http://takaful-sso.srv.aeliasoft.com/"
  KEYCLOAK_CLIENT_ID: "flutter-app"
  KEYCLOAK_REALM: "Flutter%20mobile%20app"
  KEYCLOAK_USERNAME: "username"
  KEYCLOAK_PASSWORD: "password"
  KEYCLOAK_ADMIN_USER: "user"
  KEYCLOAK_ADMIN_PASSWORD: "CT33JOswGh"
  BCRYPT_ROUNDS: "12"
  APP_LOCALE: "en"
  APP_FALLBACK_LOCALE: "en"
  APP_FAKER_LOCALE: "en_US"
  LOG_CHANNEL: "stack"
  LOG_STACK: "single"
  LOG_DEPRECATIONS_CHANNEL: "null"
  LOG_LEVEL: "debug"
  BROADCAST_CONNECTION: "log"
  FILESYSTEM_DISK: "azure"
  QUEUE_CONNECTION: "database"
  CACHE_STORE: "array"
  CACHE_PREFIX:
  MEMCACHED_HOST: "127.0.0.1"
  REDIS_CLIENT: "phpredis"
  REDIS_HOST: "127.0.0.1"
  REDIS_PASSWORD: "null"
  REDIS_PORT: "6379"
  MAIL_MAILER: "log"
  MAIL_HOST: "127.0.0.1"
  MAIL_PORT: "2525"
  MAIL_USERNAME: "null"
  MAIL_PASSWORD: "null"
  MAIL_ENCRYPTION: "null"
  MAIL_FROM_ADDRESS: "<EMAIL>"
  MAIL_FROM_NAME: "${APP_NAME}"
  AWS_ACCESS_KEY_ID:
  AWS_SECRET_ACCESS_KEY:
  AWS_DEFAULT_REGION: "us-east-1"
  AWS_BUCKET:
  AWS_USE_PATH_STYLE_ENDPOINT: "false"
  VITE_APP_NAME: "${APP_NAME}"
  AZURE_STORAGE_CONNECTION_STRING: "DefaultEndpointsProtocol=https;AccountName=takafulweb;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
  AZURE_STORAGE_NAME: "takafulweb"
  AZURE_STORAGE_KEY: "****************************************************************************************"
  AZURE_STORAGE_CONTAINER: "images"
  AZURE_STORAGE_URL: "https://takafulweb.blob.core.windows.net/"
  AZURE_STORAGE_CONTAINER_public: "mobile"
  AZURE_SAS_Token: "sv=2024-11-04&ss=bfqt&srt=sco&sp=rwdlacupiytfx&se=2028-06-22T20:22:13Z&st=2025-06-22T12:22:13Z&spr=https&sig=lPZkR5xR5DPwPzB1L%2FGxcpwKJWA4AmYFrfiMZWG1fsQ%3D"
