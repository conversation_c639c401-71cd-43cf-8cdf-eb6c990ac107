apiVersion: apps/v1
kind: Deployment
metadata:
  name: takafuldev-wf
  namespace: takaful-dev
  labels:
    app: takafuldev-wf
spec:
  replicas: 1
  selector:
    matchLabels:
      app: takafuldev-wf
  template:
    metadata:
      labels:
        app: takafuldev-wf
    spec:
      containers:
        - name: takafuldev-wf
          image: @image-name@
          ports:
            - containerPort: 80
              name: takafuldev
          envFrom:
            - configMapRef:
                name: takafuldev-wf-config
          volumeMounts:
            - name: storage
              mountPath: /var/www/html/storage
      volumes:
        - name: storage
          persistentVolumeClaim:
            claimName: takafuldev-wf-pvc