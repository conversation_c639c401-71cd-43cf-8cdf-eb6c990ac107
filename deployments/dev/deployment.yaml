apiVersion: apps/v1
kind: Deployment
metadata:
  name: takaful-webflow
  namespace: takaful-dev
  labels:
    app: takaful-webflow
    environment: dev
spec:
  replicas: 2
  selector:
    matchLabels:
      app: takaful-webflow
  template:
    metadata:
      labels:
        app: takaful-webflow
        environment: dev
    spec:
      imagePullSecrets:
      - name: acr-docker-config
      containers:
      - name: takaful-webflow
        image: "@image-name@"
        ports:
        - containerPort: 80
          name: http
        envFrom:
        - configMapRef:
            name: takafuldev-wf-config
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: storage-volume
          mountPath: /var/www/html/storage
      volumes:
      - name: storage-volume
        persistentVolumeClaim:
          claimName: takaful-webflow-storage-pvc