# Deploying Laravel Project on Azure

This guide will help you deploy this Laravel project on Microsoft Azure with an **Oracle Database**. Follow these steps carefully to ensure a successful deployment.

## Prerequisites

1. Azure Account with an active subscription
2. Azure CLI installed locally
3. Git installed
4. Composer installed
5. PHP 8.0 or higher
6. Node.js and NPM (for asset compilation)
7. Access to an Oracle Database (on Azure VM, Oracle Cloud, or on-premises)

## Step 1: Prepare Your Azure Environment

1. Create an Azure App Service:

```bash
# Login to Azure
az login

# Create a resource group (if not exists)
az group create --name takaful-rg --location eastus

# Create an App Service plan
az appservice plan create --name takaful-plan --resource-group takaful-rg --sku B1

# Create a web app
az webapp create --name takaful-app --resource-group takaful-rg --plan takaful-plan --runtime "PHP|8.0"
```

2. **Provision Oracle Database**

- You can use Oracle Database on an Azure VM (from Azure Marketplace) or connect to an Oracle Database hosted elsewhere (e.g., Oracle Cloud or on-premises).
- Ensure your Oracle DB is accessible from your Azure App Service (configure networking/firewall as needed).

3. Create Azure Storage Account (for file uploads):

```bash
# Create storage account
az storage account create \
    --name takafulstore \
    --resource-group takaful-rg \
    --location eastus \
    --sku Standard_LRS

# Get storage account connection string
az storage account show-connection-string \
    --name takafulstore \
    --resource-group takaful-rg
```

## Step 2: Configure Your Laravel Application

1. **Install Oracle DB Driver for Laravel**

Add the [yajra/laravel-oci8](https://github.com/yajra/laravel-oci8) package:

```bash
composer require yajra/laravel-oci8:"^8.0"
```

2. Update your `.env` file with Oracle configurations:

```env
APP_NAME=Takaful
APP_ENV=production
APP_DEBUG=false
APP_URL=https://takaful-app.azurewebsites.net

DB_CONNECTION=oracle
DB_HOST=<your-oracle-host>
DB_PORT=1521
DB_DATABASE=<your-service-name-or-sid>
DB_USERNAME=<your-oracle-username>
DB_PASSWORD=<your-oracle-password>
DB_SERVICE_NAME=<your-service-name> # Only if using service name

AZURE_STORAGE_NAME=takafulstore
AZURE_STORAGE_KEY=<your-storage-key>
AZURE_STORAGE_CONTAINER=<your-container-name>
AZURE_STORAGE_URL=https://takafulstore.blob.core.windows.net

CACHE_DRIVER=file
SESSION_DRIVER=database
QUEUE_CONNECTION=database

LOG_CHANNEL=stack
LOG_LEVEL=error
```

3. Update `config/database.php` to include the Oracle connection:

```php
'oracle' => [
    'driver'         => 'oracle',
    'tns'            => env('DB_TNS', ''),
    'host'           => env('DB_HOST', ''),
    'port'           => env('DB_PORT', '1521'),
    'database'       => env('DB_DATABASE', ''),
    'username'       => env('DB_USERNAME', ''),
    'password'       => env('DB_PASSWORD', ''),
    'charset'        => env('DB_CHARSET', 'AL32UTF8'),
    'prefix'         => env('DB_PREFIX', ''),
    'prefix_schema'  => env('DB_SCHEMA_PREFIX', ''),
    'edition'        => env('DB_EDITION', 'ora$base'),
    'server_version' => env('DB_SERVER_VERSION', '11g'),
],
```

4. Create deployment script (save as `.deployment`):

```text
[config]
command = bash deploy.sh
```

5. Create deploy.sh:

```bash
#!/bin/bash

# Navigate to project folder
cd /home/<USER>/wwwroot

# Install dependencies
composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev

# Clear cache
php artisan cache:clear
php artisan config:clear
php artisan view:clear

# Generate key if not already set
php artisan key:generate --force

# Run migrations (ensure your migrations are compatible with Oracle)
php artisan migrate --force

# Install and compile frontend assets
npm install
npm run build

# Optimize
php artisan optimize
```

## Step 3: Deploy to Azure

1. Configure deployment credentials:

```bash
az webapp deployment user set --user-name <username> --password <password>
```

2. Configure deployment source:

```bash
az webapp deployment source config-local-git --name takaful-app --resource-group takaful-rg
```

3. Add Azure remote to your git repository:

```bash
git remote add azure <git-url-from-previous-command>
```

4. Push to Azure:

```bash
git push azure main
```

## Step 4: Configure Azure Web App

1. Set PHP version and configurations:

```bash
# Set PHP version
az webapp config set --name takaful-app --resource-group takaful-rg --php-version "8.0"

# Update PHP configurations
az webapp config appsettings set --name takaful-app --resource-group takaful-rg --settings PHP_INI_SCAN_DIR="/usr/local/etc/php/conf.d:/home/<USER>/ini"
```

2. Create custom php.ini settings (save as `/home/<USER>/ini/custom.ini`):

```ini
upload_max_filesize = 10M
post_max_size = 10M
memory_limit = 256M
max_execution_time = 180
```

## Step 5: SSL and Domain Configuration

1. Enable HTTPS:

```bash
az webapp update --name takaful-app --resource-group takaful-rg --https-only true
```

2. Add custom domain (optional):

```bash
az webapp config hostname add --webapp-name takaful-app --resource-group takaful-rg --hostname your-domain.com
```

## Step 6: Monitoring and Maintenance

1. Enable application logging:

```bash
az webapp log config --name takaful-app --resource-group takaful-rg --application-logging filesystem --level information
```

2. View logs:

```bash
az webapp log tail --name takaful-app --resource-group takaful-rg
```

## Common Issues and Solutions

1. **Oracle Database Connection Issues**

   - Ensure Oracle DB is accessible from Azure App Service (network/firewall)
   - Use correct Oracle connection parameters (host, port, service name/SID)
   - Install Oracle Instant Client if required by your App Service
   - Ensure `yajra/laravel-oci8` is installed and configured

2. **Storage Permission Issues**

   - Ensure proper CORS configuration for Azure Storage
   - Verify storage connection string in environment variables

3. **Performance Issues**
   - Enable PHP OPcache
   - Configure proper cache drivers
   - Use Azure CDN for static assets

## Security Considerations

1. Enable Azure Web Application Firewall (WAF)
2. Configure CORS policies
3. Enable Azure DDoS Protection
4. Use Azure Key Vault for sensitive information
5. Regular security patches and updates

## Backup and Disaster Recovery

1. Configure automated backups:

```bash
az webapp backup create --resource-group takaful-rg --webapp-name takaful-app --container-url <storage-url> --backup-name initial
```

2. Schedule regular backups:

```bash
az webapp backup schedule --resource-group takaful-rg --webapp-name takaful-app --container-url <storage-url> --frequency 1d
```

## Scaling and Performance

1. Configure autoscaling:

```bash
az monitor autoscale create --resource-group takaful-rg --name autoscale-takaful --resource takaful-plan --min-count 1 --max-count 3 --count 1
```

2. Enable Azure Front Door for better performance and availability.

## Support and Resources

- Azure Support: https://azure.microsoft.com/support/options/
- Laravel Documentation: https://laravel.com/docs
- Laravel Oracle (yajra/laravel-oci8): https://github.com/yajra/laravel-oci8
- Azure Web Apps Documentation: https://docs.microsoft.com/azure/app-service/
- Azure Storage Documentation: https://docs.microsoft.com/azure/storage/

Remember to replace placeholder values (like `<your-oracle-host>`, `<your-oracle-username>`, `<your-secure-password>`, `<storage-url>`, etc.) with your actual values before using the commands.
