{"name": "sneat-bootstrap-html-laravel-admin-template-free", "version": "2.0.0", "private": true, "type": "module", "license": "MIT", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"@babel/core": "7.23.7", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-transform-object-rest-spread": "7.23.4", "@babel/plugin-transform-runtime": "7.23.7", "@babel/preset-env": "7.23.8", "@prettier/plugin-php": "0.22.1", "@rollup/plugin-html": "^1.0.3", "@types/typeahead": "^0.11.32", "ajv": "^8.17.1", "autoprefixer": "^10.4.19", "axios": "^1.7.2", "babel-loader": "^9.1.3", "browser-sync": "^2.29.3", "cross-env": "^7.0.3", "glob": "^10.4.5", "lodash": "^4.17.21", "postcss": "^8.4.40", "prettier": "3.2.2", "resolve-url-loader": "5.0.0", "sass": "~1.76.0", "sass-loader": "14.0.0", "vite": "^6.1.1"}, "overrides": {"prop-types": "15.8.1"}, "resolutions": {"prop-types": "15.8.1", "sass": "1.76.0"}, "browserslist": [">= 1%", "last 2 versions", "not dead", "Chrome >= 45", "Firefox >= 38", "Edge >= 12", "Explorer >= 10", "iOS >= 9", "Safari >= 9", "Android >= 4.4", "Opera >= 30"], "babel": {"presets": [["@babel/env", {"targets": {"browsers": [">= 1%", "last 2 versions", "not dead", "Chrome >= 45", "Firefox >= 38", "Edge >= 12", "Explorer >= 10", "iOS >= 9", "Safari >= 9", "Android >= 4.4", "Opera >= 30"]}}]]}, "dependencies": {"@popperjs/core": "^2.11.8", "apexcharts-clevision": "^3.28.5", "bootstrap": "~5.3.3", "boxicons": "~2.1.4", "highlight.js": "~11.9.0", "jquery": "~3.7.1", "laravel-vite-plugin": "^1.2.0", "masonry-layout": "~4.2.2", "perfect-scrollbar": "~1.5.5"}}