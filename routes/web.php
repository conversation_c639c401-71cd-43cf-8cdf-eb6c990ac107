<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\dashboard\Analytics;
use App\Http\Controllers\layouts\WithoutMenu;
use App\Http\Controllers\layouts\WithoutNavbar;
use App\Http\Controllers\layouts\Fluid;
use App\Http\Controllers\layouts\Container;
use App\Http\Controllers\layouts\Blank;
use App\Http\Controllers\pages\AccountSettingsAccount;
use App\Http\Controllers\pages\AccountSettingsNotifications;
use App\Http\Controllers\pages\AccountSettingsConnections;
use App\Http\Controllers\pages\MiscError;
use App\Http\Controllers\pages\MiscUnderMaintenance;
use App\Http\Controllers\authentications\LoginBasic;
use App\Http\Controllers\authentications\LoginController;
use App\Http\Controllers\authentications\RegisterBasic;
use App\Http\Controllers\authentications\ForgotPasswordBasic;
use App\Http\Controllers\cards\CardBasic;
use App\Http\Controllers\user_interface\Accordion;
use App\Http\Controllers\user_interface\Alerts;
use App\Http\Controllers\user_interface\Badges;
use App\Http\Controllers\user_interface\Buttons;
use App\Http\Controllers\user_interface\Carousel;
use App\Http\Controllers\user_interface\Collapse;
use App\Http\Controllers\user_interface\Dropdowns;
use App\Http\Controllers\user_interface\Footer;
use App\Http\Controllers\user_interface\ListGroups;
use App\Http\Controllers\user_interface\Modals;
use App\Http\Controllers\user_interface\Navbar;
use App\Http\Controllers\user_interface\Offcanvas;
use App\Http\Controllers\user_interface\PaginationBreadcrumbs;
use App\Http\Controllers\user_interface\Progress;
use App\Http\Controllers\user_interface\Spinners;
use App\Http\Controllers\user_interface\TabsPills;
use App\Http\Controllers\user_interface\Toasts;
use App\Http\Controllers\user_interface\TooltipsPopovers;
use App\Http\Controllers\user_interface\Typography;
use App\Http\Controllers\extended_ui\PerfectScrollbar;
use App\Http\Controllers\extended_ui\TextDivider;
use App\Http\Controllers\icons\Boxicons;
use App\Http\Controllers\form_elements\BasicInput;
use App\Http\Controllers\form_elements\InputGroups;
use App\Http\Controllers\form_layouts\VerticalForm;
use App\Http\Controllers\form_layouts\HorizontalForm;
use App\Http\Controllers\tables\Basic as TablesBasic;
use App\Http\Middleware\RedirectIfNotAuthenticated;
use App\Http\Middleware\RedirectIfAuthenticated;

use App\Http\Controllers\Takaful\InspectorController;
use App\Http\Controllers\Takaful\AnnouncesController;
use App\Http\Controllers\Takaful\FormsController;
use App\Http\Controllers\Takaful\VehicleController;
use App\Http\Controllers\Takaful\InsuranceOrderController;
use App\Http\Controllers\Takaful\TravelOrderController;
use App\Http\Controllers\Takaful\HomeOrderController;
use App\Http\Controllers\Takaful\IndividualOrderController;
use App\Http\Controllers\Takaful\ElevatorController;
use App\Http\Controllers\AzureImageController;
use App\Http\Controllers\Takaful\VerifyOtpController;

putenv('CURL_CA_BUNDLE=C:\php\extras\ssl\cacert.pem');

use MicrosoftAzure\Storage\Blob\BlobRestProxy;
use Illuminate\Http\Request;
use MicrosoftAzure\Storage\Blob\Models\BlobSasPermissions;
use MicrosoftAzure\Storage\Blob\Models\SharedAccessBlobPolicy;
use App\Helpers\AzureHelper;

// Main Page Route


// // layout
// Route::get('/layouts/without-menu', [WithoutMenu::class, 'index'])->name('layouts-without-menu');
// Route::get('/layouts/without-navbar', [WithoutNavbar::class, 'index'])->name('layouts-without-navbar');
// Route::get('/layouts/fluid', [Fluid::class, 'index'])->name('layouts-fluid');
// Route::get('/layouts/container', [Container::class, 'index'])->name('layouts-container');
// Route::get('/layouts/blank', [Blank::class, 'index'])->name('layouts-blank');

// // pages
// Route::get('/pages/account-settings-account', [AccountSettingsAccount::class, 'index'])->name('pages-account-settings-account');
// Route::get('/pages/account-settings-notifications', [AccountSettingsNotifications::class, 'index'])->name('pages-account-settings-notifications');
// Route::get('/pages/account-settings-connections', [AccountSettingsConnections::class, 'index'])->name('pages-account-settings-connections');
// Route::get('/pages/misc-error', [MiscError::class, 'index'])->name('pages-misc-error');
// Route::get('/pages/misc-under-maintenance', [MiscUnderMaintenance::class, 'index'])->name('pages-misc-under-maintenance');


// // cards
// Route::get('/cards/basic', [CardBasic::class, 'index'])->name('cards-basic');

// // User Interface
// Route::get('/ui/accordion', [Accordion::class, 'index'])->name('ui-accordion');
// Route::get('/ui/alerts', [Alerts::class, 'index'])->name('ui-alerts');
// Route::get('/ui/badges', [Badges::class, 'index'])->name('ui-badges');
// Route::get('/ui/buttons', [Buttons::class, 'index'])->name('ui-buttons');
// Route::get('/ui/carousel', [Carousel::class, 'index'])->name('ui-carousel');
// Route::get('/ui/collapse', [Collapse::class, 'index'])->name('ui-collapse');
// Route::get('/ui/dropdowns', [Dropdowns::class, 'index'])->name('ui-dropdowns');
// Route::get('/ui/footer', [Footer::class, 'index'])->name('ui-footer');
// Route::get('/ui/list-groups', [ListGroups::class, 'index'])->name('ui-list-groups');
// Route::get('/ui/modals', [Modals::class, 'index'])->name('ui-modals');
// Route::get('/ui/navbar', [Navbar::class, 'index'])->name('ui-navbar');
// Route::get('/ui/offcanvas', [Offcanvas::class, 'index'])->name('ui-offcanvas');
// Route::get('/ui/pagination-breadcrumbs', [PaginationBreadcrumbs::class, 'index'])->name('ui-pagination-breadcrumbs');
// Route::get('/ui/progress', [Progress::class, 'index'])->name('ui-progress');
// Route::get('/ui/spinners', [Spinners::class, 'index'])->name('ui-spinners');
// Route::get('/ui/tabs-pills', [TabsPills::class, 'index'])->name('ui-tabs-pills');
// Route::get('/ui/toasts', [Toasts::class, 'index'])->name('ui-toasts');
// Route::get('/ui/tooltips-popovers', [TooltipsPopovers::class, 'index'])->name('ui-tooltips-popovers');
// Route::get('/ui/typography', [Typography::class, 'index'])->name('ui-typography');

// // extended ui
// Route::get('/extended/ui-perfect-scrollbar', [PerfectScrollbar::class, 'index'])->name('extended-ui-perfect-scrollbar');
// Route::get('/extended/ui-text-divider', [TextDivider::class, 'index'])->name('extended-ui-text-divider');

// // icons
// Route::get('/icons/boxicons', [Boxicons::class, 'index'])->name('icons-boxicons');

// // form elements
// Route::get('/forms/basic-inputs', [BasicInput::class, 'index'])->name('forms-basic-inputs');
// Route::get('/forms/input-groups', [InputGroups::class, 'index'])->name('forms-input-groups');

// // form layouts
// Route::get('/form/layouts-vertical', [VerticalForm::class, 'index'])->name('form-layouts-vertical');
// Route::get('/form/layouts-horizontal', [HorizontalForm::class, 'index'])->name('form-layouts-horizontal');

// tables
Route::get('/tables/basic', [TablesBasic::class, 'index'])->name('tables-basic');

Route::get('/upload-image', function () {
    return view('upload');
    //disabled
});


Route::post('/upload-image-service', [AzureImageController::class, 'upload']);
Route::get('/show-image', [AzureImageController::class, 'show']);

//Takaful Routes Porject

Route::post('/upload-image-azure', function (Request $request) {
  $file = $request->file('image');
  $result = AzureHelper::uploadImage($file);
  $data = json_decode($result->getContent(), true);
  // dd($data);
  // $data['filename'] = $file->getClientOriginalName();
  // dd($data['filename']);
  // // تحقق من وجود صورة صالحة
  // $request->validate([
  //     'image' => 'required|image|max:2048', // أقصى حجم 2 ميجا
  // ]);


  $filename = time() . '_' . $file->getClientOriginalName();

  $connectionString = env('AZURE_STORAGE_CONNECTION_STRING');
  $container = env('AZURE_STORAGE_CONTAINER');

  // استخراج AccountName و AccountKey من connection string
  preg_match('/AccountName=([^;]+)/', $connectionString, $accountMatches);
  preg_match('/AccountKey=([^;]+)/', $connectionString, $keyMatches);

  if (!$accountMatches || !$keyMatches) {
      return response()->json([
          'success' => false,
          'message' => 'Invalid Azure connection string.',
      ], 500);
  }

  $accountName = $accountMatches[1];
  $accountKey = $keyMatches[1];
  $url = "https://{$accountName}.blob.core.windows.net/{$container}/{$filename}";

  // محتوى الملف وحجمه
  $fileContent = file_get_contents($file->getRealPath());
  $contentLength = strlen($fileContent);
  $mimeType = $file->getMimeType();

  // تاريخ الطلب بصيغة GMT
  $date = gmdate('D, d M Y H:i:s T');

  // رؤوس Azure المطلوبة
  $canonicalizedHeaders =
      "x-ms-blob-type:BlockBlob\n" .
      "x-ms-date:$date\n" .
      "x-ms-version:2020-04-08\n";

  // المورد المخصص
  $canonicalizedResource = "/{$accountName}/{$container}/{$filename}";

  // تصحيح Content-Length (يجب أن يكون "0" إذا الملف فارغ، هنا افتراضياً ليس فارغ)
  $contentLengthStr = $contentLength > 0 ? $contentLength : "0";

  // بناء سلسلة التوقيع حسب معايير Azure
  $stringToSign =
      "PUT\n" .                  // VERB
      "\n" .                    // Content-Encoding
      "\n" .                    // Content-Language
      $contentLengthStr . "\n" .// Content-Length
      "\n" .                    // Content-MD5
      $mimeType . "\n" .        // Content-Type
      "\n" .                    // Date (فارغ لأننا نستخدم x-ms-date)
      "\n" .                    // If-Modified-Since
      "\n" .                    // If-Match
      "\n" .                    // If-None-Match
      "\n" .                    // If-Unmodified-Since
      "\n" .                    // Range
      $canonicalizedHeaders .
      $canonicalizedResource;

  // حساب التوقيع HMAC SHA256
  $signature = base64_encode(
      hash_hmac('sha256', $stringToSign, base64_decode($accountKey), true)
  );
  $authorization = "SharedKey {$accountName}:{$signature}";
  // تنفيذ طلب CURL مع ترويسة التوثيق
  $ch = curl_init();
  curl_setopt($ch, CURLOPT_URL, $url);
  curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
  curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
  curl_setopt($ch, CURLOPT_POSTFIELDS, $fileContent);
  curl_setopt($ch, CURLOPT_HTTPHEADER, [
      "Authorization: {$authorization}",
      "x-ms-blob-type: BlockBlob",
      "x-ms-date: {$date}",
      "x-ms-version: 2020-04-08",
      "Content-Type: {$mimeType}",
      "Content-Length: {$contentLengthStr}",
  ]);

  // يمكنك تعطيل التحقق من SSL للتجربة (لا تستخدمه في الإنتاج)
  curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
  curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
  curl_setopt($ch, CURLOPT_SSLVERSION, CURL_SSLVERSION_TLSv1_2);
  curl_setopt($ch, CURLOPT_TIMEOUT, 30);

  $response = curl_exec($ch);
  $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
  $error = curl_error($ch);
  curl_close($ch);

  if ($error) {
      \Log::error('Direct cURL Upload Error: ' . $error);
      return response()->json([
          'success' => false,
          'message' => 'Upload failed: ' . $error,
      ], 500);
  }

  if ($httpCode >= 200 && $httpCode < 300) {
      return response()->json([
          'success' => true,
          'message' => 'Image uploaded successfully!',
          'filename' => $filename,
          'url' => $url,
      ]);
  } else {
      \Log::error("Direct cURL Upload HTTP Error: {$httpCode} - {$response}");
      return response()->json([
          'success' => false,
          'message' => "Upload failed: HTTP {$httpCode}",
          'details' => $response,
      ], 500);
  }
});

// authentication
Route::get('/db-test', function () {
  try {
    $connection = DB::connection('oracle');
    $pdo = $connection->getPdo();

    // استعلام بسيط لعدد الجداول في قاعدة البيانات
    $tablesCountResult = $connection->select("SELECT COUNT(*) AS count FROM user_tables");
    $tablesCount = $tablesCountResult[0]->count ?? 0;
    $tables = $connection->select("SELECT table_name FROM user_tables ORDER BY table_name");

    $tableNames = array_map(fn($table) => $table->table_name, $tables);

    return [
        'driver'        => $connection->getDriverName(),
        'host'          => config('database.connections.oracle.host'),
        'port'          => config('database.connections.oracle.port'),
        'database'      => config('database.connections.oracle.database'),
        'username'      => config('database.connections.oracle.username'),
        'connection_ok' => $pdo ? '✅ Connected' : '❌ Not connected',
        'tables_count'  => $tablesCount,
        'tables' => $tableNames
    ];
} catch (\Exception $e) {
    return [
        'connection_ok' => '❌ Failed',
        'error'         => $e->getMessage(),
    ];
}
});
Route::middleware([RedirectIfAuthenticated::class])->group(function () {
    Route::get('/auth/login', [LoginBasic::class, 'index'])->name('auth-login-basic');
    Route::post('/auth/logout', [LoginBasic::class, 'logout']);

    Route::get('/auth/register', [RegisterBasic::class, 'index'])->name('auth-register-basic');
    Route::get('/auth/forgot-password-basic', [ForgotPasswordBasic::class, 'index'])->name('auth-reset-password-basic');
    Route::post('/send-otp-reset-password', [ForgotPasswordBasic::class, 'sendVerificationCode'])->name('send.code');
    Route::post('/verify/otp/reset-password', [ForgotPasswordBasic::class, 'verifyOtp'])->name('verify.reset.code');
    Route::post('/reset-password', [ForgotPasswordBasic::class, 'resetPassword'])->name('resetPassword');
    Route::view('/verify-otp', 'content.authentications.code-reset-password')->name('verify.otp.page');
    Route::view('/rest-page', 'content.authentications.reset-passwrod-basic')->name('reset.page');
    Route::post('/login', [LoginBasic::class, 'login'])->name('login.post');
    Route::post('/register', [RegisterBasic::class, 'register'])->name('register.post');
});

Route::middleware([RedirectIfNotAuthenticated::class])->group(function () {
    Route::resource('inspectors', InspectorController::class);
    Route::resource('investigator', AnnouncesController::class);
    Route::get('/', [Analytics::class, 'index'])->name('dashboard-analytics');
    Route::post('/verify-sms-code', [VerifyOtpController::class, 'verifyCode'])->name('verify.code');
    Route::post('/send-sms-code', [VerifyOtpController::class, 'sendVerificationCode'])->name('send.otp');
    // Route::get('calculator-forms', [VehicleController::class, 'calcForms']);
    Route::get('/fetch-vehicle-specs/{id}', [VehicleController::class, 'fetchVehcilSpesUses']);
    // Route::get('main-page', [InsuranceOrderController::class, 'index']);
    Route::get('/fetch-vehicle-specs/{id}', [VehicleController::class, 'fetchVehcilSpesUses']);
    Route::post('/insurance-orders/{id}/update-status', [InsuranceOrderController::class, 'updateStatus'])->name('insurance-orders.updateStatus');
    Route::post('/insurance-orders/{id}/update-note', [InsuranceOrderController::class, 'updateOtherNote'])->name('insurance-orders.updateOtherNote');
    Route::get('/fetch-vehicle-model/{id}', [VehicleController::class, 'fetchVehcilModel']);

    Route::resource('travel-orders', TravelOrderController::class);
    Route::resource('home-orders', HomeOrderController::class);
    Route::resource('individual-orders', IndividualOrderController::class);
    Route::resource('elevator-orders', ElevatorController::class);
    Route::resource('insurance-orders', InsuranceOrderController::class);
    Route::resource('vehicle-forms', VehicleController::class);
    Route::get('/insurance-orders/{id}/renew', [InsuranceOrderController::class, 'renew'])->name('insurance-orders.renew');
    // Route::prefix('insurance-orders')->group(function () {
    //   Route::get('/', [InsuranceOrderController::class, 'index']); // List all orders
    //   Route::post('/', [InsuranceOrderController::class, 'store']); // Create order + details
    //   Route::get('/{id}', [InsuranceOrderController::class, 'show']); // View single order + details
    //   Route::put('/{id}', [InsuranceOrderController::class, 'update']); // Update order + details
    //   Route::delete('/{id}', [InsuranceOrderController::class, 'destroy']); // Delete order
    // });
    // OTP Verification
Route::get('/auth/otp',
   [VerifyOtpController::class, 'index']
)->name('otp.page');
});

Route::get('/insurance-orders/external-preview', function () {
    return view('content.takaful.takaful-forms.vehicle.external-preview');
})->name('insurance-orders.external-preview');

Route::get('/external-policy/{id}', function ($id) {
    return view('content.takaful.takaful-forms.vehicle.external-policy', ['external_id' => $id]);
})->name('external-policy.show');

Route::get('/external-edit/{chasiNo}', [InsuranceOrderController::class, 'show'])->name('external-edit.show');
