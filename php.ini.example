; PHP Configuration for Azure Blob Storage SSL
; Copy this to your php.ini file or set these values in your application

; SSL Certificate Configuration
curl.cainfo = "C:\php\extras\ssl\cacert.pem"
openssl.cafile = "C:\php\extras\ssl\cacert.pem"

; Alternative paths for different systems
; curl.cainfo = "/path/to/cacert.pem"
; openssl.cafile = "/path/to/cacert.pem"

; cURL Settings
curl.cainfo = "C:\php\extras\ssl\cacert.pem"

; OpenSSL Settings
openssl.cafile = "C:\php\extras\ssl\cacert.pem"

; Additional SSL settings
openssl.capath = "C:\php\extras\ssl"

; Timeout settings
default_socket_timeout = 60
max_execution_time = 300
memory_limit = 256M

; File upload settings
upload_max_filesize = 10M
post_max_size = 10M
max_file_uploads = 20
