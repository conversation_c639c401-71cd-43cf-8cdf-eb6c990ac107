<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('travel_passengers', function (Blueprint $table) {
          $table->id();
          $table->unsignedBigInteger('insurance_order_id');
          $table->unsignedBigInteger('travel_insurance_details_id');

          $table->integer('pass_seq')->nullable();
          $table->string('pass_passport_no')->nullable();
          $table->string('pass_aname')->nullable(); // Arabic or alt name
          $table->string('pass_lname')->nullable();
          $table->date('passport_edate')->nullable();
          $table->date('pass_dob')->nullable();
          $table->string('pass_mob')->nullable();
          $table->string('pass_address')->nullable();
          $table->decimal('pass_prem', 10, 2)->nullable();
          $table->string('pass_passport_type')->nullable(); // could be enum or varchar
          $table->timestamps(); // created_at and updated_at
          $table->softDeletes();
          // Optionally add foreign key constraints
          // $table->foreign('insurance_order_id')->references('id')->on('insurance_orders')->onDelete('cascade');
          // $table->foreign('travel_insurance_details_id')->references('id')->on('travel_insurance_details')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('travel_passengers');
    }
};
