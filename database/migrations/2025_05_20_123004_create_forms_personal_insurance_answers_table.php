<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('forms_personal_insurance_answers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('insurance_order_id');
            $table->unsignedBigInteger('individual_insurance_detail_id');
            $table->integer('question_id')->nullable();
            $table->string('question_text')->nullable();
            $table->string('answer')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            // Add foreign key constraints
            $table->foreign('insurance_order_id')
                  ->references('id')
                  ->on('insurance_orders')
                  ->onDelete('cascade');
            $table->foreign('individual_insurance_detail_id')
                  ->references('id')
                  ->on('individual_insurance_details')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('forms_personal_insurance_answers');
    }
};
