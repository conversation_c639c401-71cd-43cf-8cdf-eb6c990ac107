<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('travel_insurance_details', function (Blueprint $table) {
          $table->id();
          $table->unsignedBigInteger('insurance_order_id');
          $table->unsignedBigInteger('user_id');

          $table->unsignedInteger('p_travel_to')->nullable();
          $table->date('p_ins_start_dt')->nullable();
          $table->date('p_ins_end_dt')->nullable();
          $table->boolean('p_covid')->default(false);
          $table->decimal('p_si', 12, 2)->nullable();

          $table->integer('p_age_1')->nullable();
          $table->integer('p_age_2')->nullable();
          $table->integer('p_age_3')->nullable();
          $table->integer('p_age_4')->nullable();
          $table->integer('p_age_5')->nullable();

          $table->decimal('p_prem', 12, 2)->nullable();
          $table->decimal('p_fees', 12, 2)->nullable();

          $table->integer('number_of_travelers')->nullable();

          $table->string('insured_full_name')->nullable();
          $table->string('customer_identity')->nullable();
          $table->string('insured_identity')->nullable();
          $table->date('insured_date_of_birth')->nullable();
          $table->string('insured_email')->nullable();
          $table->string('insured_phone')->nullable();
          $table->string('insured_en_full_name')->nullable();

          $table->string('major')->nullable();
          $table->string('minor')->nullable();

          $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('travel_insurance_details');
    }
};
