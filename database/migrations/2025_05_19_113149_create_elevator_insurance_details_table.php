<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('elevator_insurance_details', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('insurance_order_id');
            $table->unsignedBigInteger('user_id');

            $table->string('p_elevator_type')->nullable();
            $table->string('p_elevator_cat')->nullable();//iprod_elevator_cat
            $table->string('p_si')->nullable(); // Sum insured
            $table->integer('p_no_passengers')->nullable();
            $table->integer('p_prod_year')->nullable();
            $table->integer('p_no_floors')->nullable();

            $table->date('p_ins_start_dt')->nullable();
            $table->date('p_ins_end_dt')->nullable();

            $table->decimal('p_perc', 8, 2)->nullable();
            $table->decimal('p_min_prem', 10, 2)->nullable();
            $table->decimal('p_prem', 10, 2)->nullable();
            $table->decimal('p_fees', 10, 2)->nullable();

            $table->string('location')->nullable();

            $table->string('insured_full_name')->nullable();
            $table->string('customer_identity')->nullable();
            $table->string('insured_identity')->nullable();
            $table->date('insured_date_of_birth')->nullable();
            $table->string('insured_email')->nullable();
            $table->string('insured_phone')->nullable();

            $table->string('building_name');
            $table->string('building_type')->nullable();
            $table->text('full_address')->nullable();
            $table->string('city')->nullable();
            $table->string('street')->nullable();
            $table->string('building_number')->nullable();
            $table->string('coverage_ceiling')->nullable(); // سقف التغطية

            $table->string('iprod_elevator_manuf_type')->nullable(); // سقف التغطية


            $table->timestamps();

            // Optional: Add foreign keys if you want
            // $table->foreign('insurance_order_id')->references('id')->on('insurance_orders')->onDelete('cascade');
            // $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade')
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('elevator_insurance_details');
    }
};
