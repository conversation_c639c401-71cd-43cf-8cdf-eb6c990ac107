<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('home_answer_qustion', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('insurance_order_id');
            $table->unsignedBigInteger('home_insurance_details');
            $table->integer('question_id')->nullable();
            $table->string('question_text')->nullable();
            $table->string('answer')->nullable();
            $table->timestamps(); // created_at and updated_at
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('home_answer_qustion');
    }
};
