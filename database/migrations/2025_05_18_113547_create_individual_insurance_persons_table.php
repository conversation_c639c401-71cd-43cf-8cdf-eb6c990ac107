<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('individual_insurance_persons', function (Blueprint $table) {
          $table->id();
          $table->unsignedBigInteger('insurance_order_id');
          $table->unsignedBigInteger('individual_insurance_detail_id');
          $table->string('cust_aname'); // Arabic name
          $table->string('cust_lname'); // Last name
          $table->unsignedBigInteger('cus_user_id')->nullable(); // if linked to an existing user
          $table->string('cust_id'); // e.g., national ID or similar
          $table->string('cust_mob'); // mobile number
          $table->date('cust_dob'); // date of birth
          $table->string('cust_address')->nullable();
          $table->string('cust_email')->nullable();
          $table->string('cust_branch')->nullable(); // branch name or code

          $table->timestamps();

          // Foreign key linking back to the individual insurance detail
          $table->foreign('individual_insurance_detail_id', 'fk_insurance_detail_id')
          ->references('id')
          ->on('individual_insurance_details')
          ->onDelete('cascade');
    

          // Optional foreign key if users are from your main users table
          // $table->foreign('cus_user_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('individual_insurance_persons');
    }
};
