<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('individual_insurance_details', function (Blueprint $table) {
          $table->id();
          $table->unsignedBigInteger('insurance_order_id');
          $table->unsignedBigInteger('user_id');

          $table->string('p_pacc_program')->nullable();
          $table->json('program_details')->nullable();

          $table->string('p_si')->nullable();
          $table->date('p_ins_start_dt')->nullable();
          $table->date('p_ins_end_dt')->nullable();

          $table->decimal('p_perc', 8, 2)->nullable();
          $table->decimal('p_min_prem', 10, 2)->nullable();
          $table->decimal('p_prem', 10, 2)->nullable();
          $table->decimal('p_fees', 10, 2)->nullable();

          $table->timestamps();

          // Optional: add foreign key constraints if needed
          // $table->foreign('insurance_order_id')->references('id')->on('insurance_orders')->onDelete('cascade');
          // $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('individual_insurance_details');
    }
};
