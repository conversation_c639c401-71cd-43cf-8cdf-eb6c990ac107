<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('home_insurance_details', function (Blueprint $table) {
          $table->id();
          $table->foreignId('insurance_order_id')->constrained('insurance_orders')->onDelete('cascade');
          $table->foreignId('user_id')->constrained('users')->onDelete('cascade');

          $table->string('p_home_type')->nullable();
          $table->string('p_home_cat')->nullable();
          $table->decimal('p_build_si', 12, 2)->nullable();
          $table->decimal('p_content_si', 12, 2)->nullable();
          $table->date('p_ins_start_dt')->nullable();
          $table->date('p_ins_end_dt')->nullable();
          $table->decimal('p_perc', 5, 2)->nullable();
          $table->decimal('p_min_prem', 12, 2)->nullable();
          $table->decimal('p_prem', 12, 2)->nullable();
          $table->decimal('p_fees', 12, 2)->nullable();

          $table->string('insured_full_name')->nullable();
          $table->string('customer_identity')->nullable();
          $table->string('insured_identity')->nullable();
          $table->date('insured_date_of_birth')->nullable();
          $table->string('insured_email')->nullable();
          $table->string('insured_phone')->nullable();

          $table->string('structure_types')->nullable();
          $table->string('location')->nullable();
          $table->string('address')->nullable();
          $table->string('street')->nullable();
          $table->string('building_number')->nullable();
          $table->string('building_floor_type')->nullable();
          $table->string('building_roof_type')->nullable();
          $table->string('building_wall_type')->nullable();
          $table->decimal('building_area', 12, 2)->nullable();
          $table->decimal('land_area', 12, 2)->nullable();

          $table->string('mortgage_holder')->nullable();
          $table->date('mortgage_end_date')->nullable();
          $table->text('notes')->nullable();
          $table->string('branch')->nullable();
          $table->string('currency')->nullable();

          $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('home_insurance_details');
    }
};
