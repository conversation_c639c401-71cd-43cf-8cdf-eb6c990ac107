<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('insurance_order_images', function (Blueprint $table) {
          $table->id();
          $table->unsignedBigInteger('insurance_order_id');
          $table->unsignedTinyInteger('insurance_type_id');
          $table->unsignedBigInteger('user_id');
          $table->unsignedBigInteger('insurance_details_id'); // New column
          $table->string('image_path');
          $table->string('type'); // Example: 'front', 'side', 'invoice'
          $table->timestamps();

          $table->foreign('insurance_order_id')->references('id')->on('insurance_orders')->onDelete('cascade');
          $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('insurance_order_images');
    }
};
