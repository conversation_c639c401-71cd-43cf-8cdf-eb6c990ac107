<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('insurance_orders', function (Blueprint $table) {
          $table->id(); // Primary key (id)
          $table->unsignedBigInteger('user_id'); // FK to users table
          $table->unsignedBigInteger('type_id'); // Foreign key to types table
          $table->text('note_1')->nullable();
          $table->text('note_2')->nullable();
          $table->decimal('total_price', 10, 2)->nullable(); // Cost (decimal)
          $table->boolean('status')->default(false); // Status (boolean)
          $table->unsignedBigInteger('approved_by')->nullable(); // Approved by (user ID)

          $table->timestamps(); // created_at and updated_at
          $table->softDeletes();

          // $table->foreign('type_id')->references('id')->on('types')->onDelete('cascade');
      });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('insurance_orders');
    }
};
