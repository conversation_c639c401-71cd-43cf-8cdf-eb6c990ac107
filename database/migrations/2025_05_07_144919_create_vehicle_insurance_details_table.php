<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicle_insurance_details', function (Blueprint $table) {
          $table->id();

          // Foreign keys
          $table->unsignedBigInteger('insurance_order_id');
          $table->unsignedBigInteger('user_id');

          // Policy information
          $table->string('policy_location')->nullable();
          $table->string('policy_use')->nullable();
          $table->string('policy_spical_use')->nullable();
          $table->string('policy_load')->nullable();
          $table->string('policy_cc')->nullable();
          $table->decimal('policy_inurance_vechile_price', 12, 2)->nullable();
          $table->integer('policy_seats')->nullable();
          $table->integer('policy_add_seats')->nullable();
          $table->integer('policy_prod_year')->nullable();
          $table->decimal('policy_days_perc', 5, 2)->nullable();
          $table->string('policy_vechile_class_type')->nullable();
          $table->date('policy_reg_date')->nullable();

          // Pricing
          $table->decimal('act_price', 10, 2)->nullable();
          $table->decimal('tp_price', 10, 2)->nullable();
          $table->decimal('comp_price', 10, 2)->nullable();
          $table->decimal('total_cost', 10, 2)->nullable();

          // Insured person information
          $table->string('insured_full_name')->nullable();
          $table->date('insured_date_of_birth')->nullable();
          $table->string('insured_identity')->nullable();
          $table->string('insured_phone')->nullable();
          $table->string('insured_location')->nullable();
          $table->date('insured_end_license_date')->nullable();
          $table->string('insured_emai')->nullable();

          // Insurance duration
          $table->date('start_insurance_date')->nullable();
          $table->date('end_insurance_date')->nullable();

          // Driver information
          $table->string('who_can_drive_name')->nullable();
          $table->string('who_can_drive_identity')->nullable();

          // Vehicle details
          $table->string('chasi_number')->nullable();
          $table->string('plate_number')->nullable();
          $table->string('vechile_type')->nullable();
          $table->string('feul_type')->nullable();

          // Images
          $table->string('left_image')->nullable();
          $table->string('right_image')->nullable();
          $table->string('back_image')->nullable();
          $table->string('front_image')->nullable();
          $table->string('driver_license_image')->nullable();
          $table->string('chasi_image')->nullable();
          $table->string('identitiy_image')->nullable();
          $table->string('vechile_llicense_image')->nullable();
          $table->string('image1')->nullable();
          $table->string('image2')->nullable();
          $table->string('image3')->nullable();

          $table->string('branch')->nullable();
          $table->string('vechile_model')->nullable();
          $table->string('young_or_new_driver')->nullable();
          

          $table->timestamps();

          // Foreign key constraints (optional)
          $table->foreign('insurance_order_id')->references('id')->on('insurance_orders')->onDelete('cascade');
          $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
      });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicle_insurance_details');
    }
};
